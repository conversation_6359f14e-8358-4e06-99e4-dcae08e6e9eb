<?php
echo "<h1>🔗 FINALIZE DYNAMIC URLS FOR DEPLOYMENT</h1>\n";
echo "<p><strong>Purpose:</strong> Ensure all URLs are dynamic and production-ready</p>\n";

require_once 'config.php';

echo "<h2>🔍 URL CONFIGURATION ANALYSIS</h2>\n";

// Check current configuration
echo "<div style='background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6;'>\n";
echo "<h3>📋 Current Configuration:</h3>\n";

if (defined('SITE_URL')) {
    echo "<p><strong>SITE_URL:</strong> " . htmlspecialchars(SITE_URL) . "</p>\n";
    
    if (strpos(SITE_URL, 'localhost') !== false || strpos(SITE_URL, '127.0.0.1') !== false) {
        echo "<div style='background: #f8d7da; padding: 10px; border: 1px solid #f5c6cb; margin: 10px 0;'>\n";
        echo "<p style='color: red; font-weight: bold;'>❌ CRITICAL: SITE_URL still uses localhost!</p>\n";
        echo "<p>For production deployment, update config.php:</p>\n";
        echo "<code>define('SITE_URL', 'https://freedomassemblydb.online');</code>\n";
        echo "</div>\n";
    } else {
        echo "<p style='color: green;'>✅ SITE_URL is production-ready</p>\n";
    }
} else {
    echo "<p style='color: red;'>❌ SITE_URL not defined</p>\n";
}

if (defined('BASE_URL')) {
    echo "<p><strong>BASE_URL:</strong> " . htmlspecialchars(BASE_URL) . "</p>\n";
} else {
    echo "<p style='color: orange;'>⚠️ BASE_URL not defined (will be auto-generated)</p>\n";
}

if (defined('ADMIN_URL')) {
    echo "<p><strong>ADMIN_URL:</strong> " . htmlspecialchars(ADMIN_URL) . "</p>\n";
} else {
    echo "<p style='color: orange;'>⚠️ ADMIN_URL not defined (will be auto-generated)</p>\n";
}

echo "</div>\n";

echo "<h2>🔍 SCANNING FOR HARDCODED URLS</h2>\n";

// Files to scan for hardcoded URLs
$filesToScan = [
    'config.php',
    'send_birthday_reminders.php',
    'admin/preview_template.php',
    'admin/proxy.php',
    'user/includes/js_vars.php'
];

$foundIssues = [];

foreach ($filesToScan as $file) {
    $fullPath = __DIR__ . '/' . $file;
    
    if (!file_exists($fullPath)) {
        echo "<p style='color: gray;'>⏭️ File not found: $file</p>\n";
        continue;
    }
    
    echo "<h3>📄 Scanning: $file</h3>\n";
    
    $content = file_get_contents($fullPath);
    $fileIssues = [];
    
    // Check for localhost URLs
    if (preg_match_all('/localhost[^"\'\s]*/', $content, $matches)) {
        $fileIssues['localhost'] = array_unique($matches[0]);
    }
    
    // Check for hardcoded campaign/church paths
    if (preg_match_all('/\/campaign\/church[^"\'\s]*/', $content, $matches)) {
        $fileIssues['hardcoded_paths'] = array_unique($matches[0]);
    }
    
    // Check for 127.0.0.1
    if (preg_match_all('/127\.0\.0\.1[^"\'\s]*/', $content, $matches)) {
        $fileIssues['localhost_ip'] = array_unique($matches[0]);
    }
    
    if (!empty($fileIssues)) {
        $foundIssues[$file] = $fileIssues;
        
        echo "<div style='background: #fff3cd; padding: 10px; border: 1px solid #ffeaa7; margin: 10px 0;'>\n";
        echo "<h4 style='color: #856404;'>⚠️ Issues found in: $file</h4>\n";
        
        foreach ($fileIssues as $type => $issues) {
            echo "<p><strong>" . ucfirst(str_replace('_', ' ', $type)) . ":</strong></p>\n";
            foreach ($issues as $issue) {
                echo "<p style='margin-left: 20px;'><code>" . htmlspecialchars($issue) . "</code></p>\n";
            }
        }
        echo "</div>\n";
    } else {
        echo "<p style='color: green;'>✅ No hardcoded URLs found in: $file</p>\n";
    }
}

echo "<h2>🔧 DYNAMIC URL VALIDATION</h2>\n";

// Test dynamic URL functions
echo "<h3>🧪 Testing URL Helper Functions:</h3>\n";

$urlTests = [
    'get_base_url()' => function_exists('get_base_url') ? get_base_url() : 'Function not found',
    'get_admin_url()' => function_exists('get_admin_url') ? get_admin_url() : 'Function not found',
    'url_for("test")' => function_exists('url_for') ? url_for('test') : 'Function not found',
    'admin_url_for("test")' => function_exists('admin_url_for') ? admin_url_for('test') : 'Function not found'
];

foreach ($urlTests as $test => $result) {
    echo "<div style='background: #e7f3ff; padding: 10px; margin: 10px 0; border-left: 4px solid #007bff;'>\n";
    echo "<p><strong>$test:</strong> " . htmlspecialchars($result) . "</p>\n";
    
    if (strpos($result, 'localhost') !== false || strpos($result, '127.0.0.1') !== false) {
        echo "<p style='color: red;'>❌ Contains localhost - will break in production</p>\n";
    } elseif ($result === 'Function not found') {
        echo "<p style='color: red;'>❌ Function not available</p>\n";
    } else {
        echo "<p style='color: green;'>✅ Dynamic URL generated correctly</p>\n";
    }
    echo "</div>\n";
}

echo "<h2>📋 PRODUCTION DEPLOYMENT CHECKLIST</h2>\n";

$deploymentChecklist = [
    'SITE_URL configured for production' => defined('SITE_URL') && strpos(SITE_URL, 'localhost') === false,
    'No hardcoded localhost URLs' => empty($foundIssues),
    'Dynamic URL functions available' => function_exists('get_base_url') && function_exists('get_admin_url'),
    'Cron jobs use dynamic URLs' => file_exists(__DIR__ . '/setup_production_cron_jobs.php'),
    'Environment detection working' => defined('BASE_URL'),
    'Config file production-ready' => file_exists(__DIR__ . '/config.php')
];

echo "<div style='background: #f8f9fa; padding: 20px; border: 1px solid #dee2e6; border-radius: 5px;'>\n";
foreach ($deploymentChecklist as $item => $status) {
    $icon = $status ? '✅' : '❌';
    $color = $status ? 'green' : 'red';
    echo "<p style='color: $color;'>$icon $item</p>\n";
}
echo "</div>\n";

echo "<h2>🚀 PRODUCTION CONFIGURATION TEMPLATE</h2>\n";

echo "<div style='background: #d4edda; padding: 20px; border: 1px solid #c3e6cb; border-radius: 5px;'>\n";
echo "<h3>📝 Required config.php Updates for Production:</h3>\n";
echo "<pre style='background: #343a40; color: #fff; padding: 15px; border-radius: 5px;'>";
echo htmlspecialchars('<?php
// Production Configuration for freedomassemblydb.online

// Define the site URL for production
define(\'SITE_URL\', \'https://freedomassemblydb.online\');

// The rest of your config.php remains the same
// All URL functions will automatically use the production SITE_URL
');
echo "</pre>\n";
echo "</div>\n";

echo "<h2>🎯 CRON JOB COMMANDS FOR PRODUCTION</h2>\n";

// Generate production cron commands
$siteUrl = 'https://freedomassemblydb.online'; // Production URL
$basePath = '/campaign/church';
$cronKey = 'fac_2024_secure_cron_8x9q2p5m';

$cronJobs = [
    ['name' => 'Birthday Reminders', 'file' => 'cron/birthday_reminders.php', 'schedule' => '0 7 * * *'],
    ['name' => 'Scheduled Emails', 'file' => 'cron/process_scheduled_emails.php', 'schedule' => '*/5 * * * *'],
    ['name' => 'Email Queue', 'file' => 'cron/process_email_queue.php', 'schedule' => '*/5 * * * *'],
    ['name' => 'Event Reminders', 'file' => 'cron/event_reminders.php', 'schedule' => '0 8 * * *'],
    ['name' => 'Birthday Gifts', 'file' => 'cron/process_birthday_gifts.php', 'schedule' => '0 9 * * *'],
    ['name' => 'System Cleanup', 'file' => 'cron/system_cleanup.php', 'schedule' => '0 2 * * 0']
];

echo "<div style='background: #343a40; color: #fff; padding: 20px; margin: 15px 0; border-radius: 5px;'>\n";
echo "<h3 style='color: #fff;'>📋 Copy these commands to your crontab:</h3>\n";
echo "<pre style='margin: 0; white-space: pre-wrap;'>";

foreach ($cronJobs as $job) {
    $url = $siteUrl . $basePath . '/' . $job['file'] . '?cron_key=' . $cronKey;
    echo "# {$job['name']}\n";
    echo "{$job['schedule']} wget -q -O /dev/null \"$url\"\n\n";
}

echo "</pre>\n";
echo "</div>\n";

echo "<h2>📊 FINAL SUMMARY</h2>\n";

if (empty($foundIssues) && defined('SITE_URL') && strpos(SITE_URL, 'localhost') === false) {
    echo "<div style='background: #d4edda; padding: 20px; border: 1px solid #c3e6cb; border-radius: 5px;'>\n";
    echo "<h3 style='color: #155724;'>🎉 READY FOR PRODUCTION DEPLOYMENT!</h3>\n";
    echo "<p>✅ All URLs are dynamic and production-ready</p>\n";
    echo "<p>✅ No hardcoded localhost references found</p>\n";
    echo "<p>✅ Configuration is properly set up</p>\n";
    echo "<p>✅ Cron jobs are configured with dynamic URLs</p>\n";
    echo "</div>\n";
} else {
    echo "<div style='background: #f8d7da; padding: 20px; border: 1px solid #f5c6cb; border-radius: 5px;'>\n";
    echo "<h3 style='color: #721c24;'>⚠️ ISSUES NEED TO BE RESOLVED</h3>\n";
    
    if (!empty($foundIssues)) {
        echo "<p>❌ Hardcoded URLs found in " . count($foundIssues) . " files</p>\n";
    }
    
    if (!defined('SITE_URL') || strpos(SITE_URL, 'localhost') !== false) {
        echo "<p>❌ SITE_URL needs to be updated for production</p>\n";
    }
    
    echo "<p><strong>Please resolve these issues before deployment.</strong></p>\n";
    echo "</div>\n";
}

echo "<h2>🔗 NEXT STEPS</h2>\n";

echo "<div style='background: #e7f3ff; padding: 20px; border: 1px solid #b3d9ff; border-radius: 5px;'>\n";
echo "<h3>📋 Deployment Steps:</h3>\n";
echo "<ol>\n";
echo "<li><strong>Update config.php:</strong> Set SITE_URL to https://freedomassemblydb.online</li>\n";
echo "<li><strong>Upload files:</strong> Upload the cleaned codebase to production server</li>\n";
echo "<li><strong>Test URLs:</strong> Verify all pages load correctly</li>\n";
echo "<li><strong>Set up cron jobs:</strong> Add the cron commands above</li>\n";
echo "<li><strong>Test functionality:</strong> Verify emails, notifications, and automated processes work</li>\n";
echo "<li><strong>Monitor logs:</strong> Check for any issues in the first 24 hours</li>\n";
echo "</ol>\n";
echo "</div>\n";

?>
