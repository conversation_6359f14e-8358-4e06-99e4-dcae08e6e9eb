<?php
// Include database connection
require_once 'config.php';
require_once 'send_birthday_reminders.php';

echo "<h2>Testing Birthday Image Alt Text Fix</h2>\n";

// Use the global $pdo connection
global $pdo;
$db = $pdo;

// Get a test member with an image
$stmt = $db->prepare("SELECT * FROM members WHERE image_path IS NOT NULL AND image_path != '' LIMIT 1");
$stmt->execute();
$testMember = $stmt->fetch();

if (!$testMember) {
    echo "<p style='color: red;'>No members with images found for testing.</p>\n";
    exit;
}

echo "<h3>Test Member: " . htmlspecialchars($testMember['full_name']) . "</h3>\n";
echo "<p>Image Path: " . htmlspecialchars($testMember['image_path']) . "</p>\n";

// Get a birthday template
$stmt = $db->prepare("SELECT * FROM birthday_templates LIMIT 1");
$stmt->execute();
$template = $stmt->fetch();

if (!$template) {
    echo "<p style='color: red;'>No active birthday templates found.</p>\n";
    exit;
}

echo "<h3>Template: " . htmlspecialchars($template['name']) . "</h3>\n";

// Create a birthday reminder instance
$birthdayReminder = new BirthdayReminder($db);

// Test the sendEmail method with birthday notification
echo "<h3>Testing Birthday Notification Email Processing</h3>\n";

// Create test recipient (different from birthday member)
$testRecipient = [
    'id' => 999,
    'full_name' => 'Test Recipient',
    'email' => '<EMAIL>',
    'image_path' => 'assets/img/default-avatar.png'
];

try {
    // Test the email processing
    $result = $birthdayReminder->sendEmail(
        $testRecipient,
        $template['subject'],
        $template['template_content'],
        $testMember, // Birthday member
        0 // Days until birthday (today)
    );
    
    if ($result) {
        echo "<p style='color: green;'>✅ Email processing completed successfully!</p>\n";
        echo "<p>Check the email logs for details about image processing.</p>\n";
    } else {
        echo "<p style='color: red;'>❌ Email processing failed.</p>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

echo "<h3>Expected Results:</h3>\n";
echo "<ul>\n";
echo "<li>✅ Image alt text should show: <strong>" . htmlspecialchars($testMember['full_name']) . "</strong></li>\n";
echo "<li>✅ Image src should point to: <strong>" . htmlspecialchars($testMember['image_path']) . "</strong></li>\n";
echo "<li>✅ Email should be about: <strong>" . htmlspecialchars($testMember['full_name']) . "'s birthday</strong></li>\n";
echo "<li>✅ Email should be sent to: <strong>" . htmlspecialchars($testRecipient['full_name']) . "</strong></li>\n";
echo "</ul>\n";

?>
