<?php
/**
 * Fix Template 1 specifically
 */

require_once 'config.php';

echo "<h2>Fixing Template 1 Specifically</h2>\n";

try {
    $stmt = $pdo->prepare('SELECT content FROM email_templates WHERE id = 37');
    $stmt->execute();
    $template = $stmt->fetch();
    
    if ($template) {
        $content = $template['content'];
        $originalContent = $content;
        
        echo "<h3>Current structure around {member_image}:</h3>\n";
        $pos = strpos($content, '{member_image}');
        if ($pos !== false) {
            $start = max(0, $pos - 200);
            $length = 400;
            $context = substr($content, $start, $length);
            echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd;'>";
            echo htmlspecialchars($context);
            echo "</pre>\n";
        }
        
        // More specific pattern for Template 1
        // It looks like: <div class="member-section">{member_image}<div class="member-name">...
        $pattern = '/(<div[^>]*class=["\']member-section["\'][^>]*>\s*)\{member_image\}(\s*<div class=["\']member-name["\'])/i';
        $replacement = '$1<img src="{member_image}" alt="{birthday_member_full_name}" style="width: 150px; height: 150px; border-radius: 50%; object-fit: cover; border: 4px solid #ddd; display: block; margin: 0 auto 15px auto;">$2';
        
        $content = preg_replace($pattern, $replacement, $content);
        
        if ($content !== $originalContent) {
            echo "<h3>Replacement successful! New structure:</h3>\n";
            $pos = strpos($content, 'src="{member_image}"');
            if ($pos !== false) {
                $start = max(0, $pos - 100);
                $length = 300;
                $context = substr($content, $start, $length);
                echo "<pre style='background: #e6ffe6; padding: 10px; border: 1px solid #99ff99;'>";
                echo htmlspecialchars($context);
                echo "</pre>\n";
            }
            
            $updateStmt = $pdo->prepare('UPDATE email_templates SET content = ? WHERE id = 37');
            $result = $updateStmt->execute([$content]);
            
            if ($result) {
                echo "<p style='color: green;'>✅ Template 1 updated successfully</p>\n";
            } else {
                echo "<p style='color: red;'>❌ Failed to update Template 1</p>\n";
            }
        } else {
            echo "<p style='color: orange;'>⚠️ No changes made - pattern didn't match</p>\n";
            
            // Try a simpler approach
            echo "<h3>Trying simpler replacement...</h3>\n";
            $content = str_replace(
                '{member_image}',
                '<img src="{member_image}" alt="{birthday_member_full_name}" style="width: 150px; height: 150px; border-radius: 50%; object-fit: cover; border: 4px solid #ddd; display: block; margin: 0 auto 15px auto;">',
                $content
            );
            
            if ($content !== $originalContent) {
                $updateStmt = $pdo->prepare('UPDATE email_templates SET content = ? WHERE id = 37');
                $result = $updateStmt->execute([$content]);
                
                if ($result) {
                    echo "<p style='color: green;'>✅ Template 1 updated with simple replacement</p>\n";
                } else {
                    echo "<p style='color: red;'>❌ Failed to update Template 1</p>\n";
                }
            }
        }
    }
    
    // Final verification
    echo "<h3>Final Verification</h3>\n";
    $stmt = $pdo->prepare('SELECT content FROM email_templates WHERE id = 37');
    $stmt->execute();
    $finalTemplate = $stmt->fetch();
    
    if ($finalTemplate && preg_match('/<img[^>]+src=["\']?\{member_image\}["\']?[^>]*>/i', $finalTemplate['content'])) {
        echo "<p style='color: green;'>✅ Template 1 now has proper img tag!</p>\n";
    } else {
        echo "<p style='color: red;'>❌ Template 1 still needs manual fixing</p>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}
?>
