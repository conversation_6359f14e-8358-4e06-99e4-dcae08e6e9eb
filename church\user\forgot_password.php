<?php
/**
 * Forgot Password Page
 * 
 * Allows users to request password reset via email
 */

session_start();

// Include configuration and classes
require_once '../config.php';
require_once '../classes/SecurityManager.php';
require_once '../classes/UserAuthManager.php';
require_once '../includes/email_functions.php';

// Initialize managers
$security = new SecurityManager($pdo);
$userAuth = new UserAuthManager($pdo, $security);

// Set security headers
$security->setSecurityHeaders();

$error = '';
$success = '';

// Redirect if already logged in
if ($userAuth->isAuthenticated()) {
    header("Location: dashboard.php");
    exit();
}

// Process forgot password form
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['reset_password'])) {
    // Validate CSRF token
    if (!isset($_POST['csrf_token']) || !$security->validateCSRFToken($_POST['csrf_token'])) {
        $error = "Invalid form submission. Please try again.";
    } else {
        $identifier = $security->sanitizeInput($_POST['identifier'], 'text');
        
        if (empty($identifier)) {
            $error = "Please enter your email address or phone number.";
        } else {
            $result = $userAuth->generatePasswordResetToken($identifier);
            
            if ($result['success']) {
                // Send password reset email
                try {
                    $resetLink = "http" . (isset($_SERVER['HTTPS']) ? "s" : "") . "://" . $_SERVER['HTTP_HOST'] . 
                                dirname($_SERVER['REQUEST_URI']) . "/reset_password.php?token=" . $result['token'];
                    
                    $to = $result['user']['email'];
                    $toName = $result['user']['full_name'];
                    $subject = 'Password Reset Request';
                    $body = "
                        <h2>Password Reset Request</h2>
                        <p>Hello {$result['user']['full_name']},</p>
                        <p>You have requested to reset your password. Click the link below to reset your password:</p>
                        <p><a href='$resetLink' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Reset Password</a></p>
                        <p>If you did not request this password reset, please ignore this email.</p>
                        <p>This link will expire in 4 hours for security reasons.</p>
                        <p>Best regards,<br>Church Management Team</p>
                    ";

                    if (sendEmail($to, $toName, $subject, $body, true, $result['user'])) {
                        $success = "Password reset instructions have been sent to your email address.";
                    } else {
                        $error = "Failed to send password reset email. Please try again or contact support.";
                    }
                } catch (Exception $e) {
                    error_log("Password reset email error: " . $e->getMessage());
                    $error = "An error occurred while sending the reset email. Please try again.";
                }
            } else {
                $error = $result['message'];
            }
        }
    }
}

// Get site settings for branding
$sitename = get_organization_name() . ' - Forgot Password';
$stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = 'site_name'");
$stmt->execute();
$siteNameResult = $stmt->fetch();
if ($siteNameResult) {
    $sitename = $siteNameResult['setting_value'];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password - <?php echo get_organization_name(); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        :root {
            --primary-color: #4f46e5;
            --primary-hover: #4338ca;
            --secondary-color: #6366f1;
            --success-color: #10b981;
            --danger-color: #ef4444;
            --warning-color: #f59e0b;
            --dark-color: #1f2937;
            --light-color: #f8fafc;
            --border-color: #e5e7eb;
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            position: relative;
            overflow-x: hidden;
        }
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
        }

        .forgot-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 24px;
            padding: 48px 40px;
            width: 100%;
            max-width: 440px;
            box-shadow: var(--shadow-xl);
            position: relative;
            animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .forgot-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .forgot-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            background: linear-gradient(135deg, var(--warning-color), #fb923c);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: var(--shadow-xl);
        }

        .forgot-icon img {
            max-width: 60px;
            max-height: 60px;
            object-fit: contain;
            border-radius: 12px;
        }

        .forgot-icon i {
            font-size: 32px;
            color: white;
        }

        .forgot-title {
            font-size: 28px;
            font-weight: 700;
            color: var(--dark-color);
            margin-bottom: 8px;
        }

        .forgot-subtitle {
            color: #6b7280;
            font-size: 16px;
            font-weight: 400;
        }

        .alert {
            border: none;
            border-radius: 12px;
            padding: 16px 20px;
            margin-bottom: 24px;
            font-weight: 500;
        }

        .alert-danger {
            background: rgba(239, 68, 68, 0.1);
            color: var(--danger-color);
            border-left: 4px solid var(--danger-color);
        }

        .alert-success {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
            border-left: 4px solid var(--success-color);
        }

        .form-floating {
            margin-bottom: 24px;
            position: relative;
        }

        .form-floating input {
            border: 2px solid var(--border-color);
            border-radius: 12px;
            padding: 16px 20px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.8);
        }

        .form-floating input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
            background: white;
        }

        .form-floating label {
            color: #6b7280;
            font-weight: 500;
        }

        .btn-reset {
            background: linear-gradient(135deg, var(--warning-color), #fb923c);
            border: none;
            border-radius: 12px;
            padding: 16px 24px;
            font-size: 16px;
            font-weight: 600;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
        }

        .btn-reset:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl);
        }

        .btn-primary-custom {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            border-radius: 12px;
            padding: 12px 24px;
            font-weight: 600;
            color: white;
            transition: all 0.3s ease;
        }

        .btn-primary-custom:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-xl);
        }

        .back-link {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            margin-top: 24px;
        }

        .back-link:hover {
            color: var(--primary-hover);
            text-decoration: underline;
        }

        .info-box {
            background: rgba(79, 70, 229, 0.05);
            border: 1px solid rgba(79, 70, 229, 0.1);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 24px;
        }

        .info-box p {
            margin: 0;
            color: #6b7280;
            line-height: 1.6;
        }

        @media (max-width: 480px) {
            .forgot-container {
                padding: 32px 24px;
                margin: 16px;
            }
        }
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="forgot-container">
        <div class="forgot-header">
            <div class="forgot-icon">
                <i class="fas fa-key"></i>
            </div>
            <h1 class="forgot-title">Reset Password</h1>
            <p class="forgot-subtitle"><?php echo htmlspecialchars(get_organization_name()); ?></p>
        </div>

        <?php if (!empty($error)): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?php echo $error; ?>
            </div>
        <?php endif; ?>

        <?php if (!empty($success)): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo $success; ?>
            </div>
            <div class="text-center">
                <a href="login.php" class="btn btn-primary-custom">
                    <i class="fas fa-arrow-left me-2"></i>
                    Return to Login
                </a>
            </div>
        <?php else: ?>
            <div class="info-box">
                <p>
                    <i class="fas fa-info-circle me-2"></i>
                    Enter your email address or phone number and we'll send you instructions to reset your password.
                </p>
            </div>

            <form method="POST" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" id="resetForm">
                <?php echo $security->generateCSRFInput(); ?>

                <div class="form-floating">
                    <input type="text" class="form-control" id="identifier" name="identifier" placeholder="Email or Phone Number" required>
                    <label for="identifier">
                        <i class="fas fa-user me-2"></i>Email or Phone Number
                    </label>
                </div>

                <button type="submit" name="reset_password" class="btn-reset" id="resetBtn">
                    <i class="fas fa-paper-plane me-2"></i>
                    Send Reset Instructions
                </button>

                <div class="text-center">
                    <a href="login.php" class="back-link">
                        <i class="fas fa-arrow-left"></i>
                        Back to Login
                    </a>
                </div>
            </form>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const resetForm = document.getElementById('resetForm');
            const resetBtn = document.getElementById('resetBtn');
            const identifierInput = document.getElementById('identifier');

            // Auto-focus identifier field
            if (identifierInput) {
                identifierInput.focus();
            }

            // Add loading state on form submission
            if (resetForm) {
                resetForm.addEventListener('submit', function(e) {
                    // Just add loading state, don't prevent submission
                    resetBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Sending...';
                    resetBtn.disabled = true;

                    // Re-enable button after 10 seconds as fallback
                    setTimeout(() => {
                        resetBtn.innerHTML = '<i class="fas fa-paper-plane me-2"></i>Send Reset Instructions';
                        resetBtn.disabled = false;
                    }, 10000);
                });
            }

            // Enhanced input focus effects
            const inputs = document.querySelectorAll('.form-floating input');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.style.transform = 'translateY(-2px)';
                    this.parentElement.style.transition = 'transform 0.3s ease';
                });

                input.addEventListener('blur', function() {
                    this.parentElement.style.transform = 'translateY(0)';
                });
            });
        });
    </script>
</body>
</html>
