<?php
require_once 'config.php';

echo "<h2>🔥 AGGRESSIVE TEMPLATE CORRUPTION FIX</h2>\n";

global $pdo;

// Get all templates that might contain the corruption
$stmt = $pdo->prepare("
    SELECT id, template_name, content 
    FROM email_templates 
    WHERE content LIKE '%Sandra Stern%' 
    OR content LIKE '%\" alt=%' 
    OR content LIKE '%style=\"width: 150px%'
    ORDER BY template_name
");
$stmt->execute();
$templates = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<p>Found " . count($templates) . " potentially corrupted templates.</p>\n";

$fixedCount = 0;

foreach ($templates as $template) {
    echo "<div style='border: 3px solid #dc3545; margin: 20px 0; padding: 20px;'>\n";
    echo "<h3>🔧 " . htmlspecialchars($template['template_name']) . " (ID: {$template['id']})</h3>\n";
    
    $content = $template['content'];
    $originalContent = $content;
    $hasChanges = false;
    
    // Show current problematic content
    if (strpos($content, 'Sandra <PERSON>') !== false) {
        echo "<h4 style='color: red;'>🚨 FOUND 'Sandra Stern' in template!</h4>\n";
        
        // Find all occurrences
        $pos = 0;
        $occurrences = [];
        while (($pos = strpos($content, 'Sandra Stern', $pos)) !== false) {
            $start = max(0, $pos - 100);
            $length = 300;
            $context = substr($content, $start, $length);
            $occurrences[] = $context;
            $pos += strlen('Sandra Stern');
        }
        
        foreach ($occurrences as $i => $context) {
            echo "<h5>Occurrence " . ($i + 1) . ":</h5>\n";
            echo "<pre style='background: #f8d7da; padding: 10px; border: 1px solid #f5c6cb; white-space: pre-wrap; font-size: 12px;'>" . htmlspecialchars($context) . "</pre>\n";
        }
    }
    
    // AGGRESSIVE FIXES
    
    // Fix 1: Remove all instances of "Sandra Stern" and replace with placeholder
    if (strpos($content, 'Sandra Stern') !== false) {
        $content = str_replace('Sandra Stern', '{birthday_member_full_name}', $content);
        $hasChanges = true;
        echo "<p style='color: green;'>✅ Replaced all 'Sandra Stern' with {birthday_member_full_name}</p>\n";
    }
    
    // Fix 2: Fix the specific broken pattern from user's message
    $brokenPatterns = [
        // Exact pattern from user
        '{birthday_member_full_name}" alt="{birthday_member_full_name}" style="width: 150px; height: 150px; border-radius: 50%; object-fit: cover; border: 4px solid #ddd; display: block; margin: 0 auto;">',
        
        // Variations
        '{birthday_member_full_name}" alt="Sandra Stern" style="width: 150px; height: 150px; border-radius: 50%; object-fit: cover; border: 4px solid #ddd; display: block; margin: 0 auto;">',
        'Sandra Stern" alt="Sandra Stern" style="width: 150px; height: 150px; border-radius: 50%; object-fit: cover; border: 4px solid #ddd; display: block; margin: 0 auto;">',
        '{birthday_member_full_name}" alt="{birthday_member_full_name}" style="width: 150px; height: 150px; border-radius: 50%; object-fit: cover; border: 4px solid #ddd; display: block; margin: 0 auto;">'
    ];
    
    $correctReplacement = '<img src="{birthday_member_image}" alt="{birthday_member_full_name}" style="width: 150px; height: 150px; border-radius: 50%; object-fit: cover; border: 4px solid #ddd; display: block; margin: 0 auto;">';
    
    foreach ($brokenPatterns as $pattern) {
        if (strpos($content, $pattern) !== false) {
            $content = str_replace($pattern, $correctReplacement, $content);
            $hasChanges = true;
            echo "<p style='color: green;'>✅ Fixed broken pattern: " . htmlspecialchars(substr($pattern, 0, 50)) . "...</p>\n";
        }
    }
    
    // Fix 3: Fix any remaining broken image patterns using regex
    $regexPatterns = [
        // Pattern: text" alt="text" style="..."
        '/([^<>]*)"(\s*alt="[^"]*")(\s*style="[^"]*"[^>]*>)/',
        // Pattern: placeholder" alt="..." style="..."
        '/(\{[^}]+\})"(\s*alt="[^"]*")(\s*style="[^"]*"[^>]*>)/'
    ];
    
    foreach ($regexPatterns as $pattern) {
        if (preg_match_all($pattern, $content, $matches, PREG_SET_ORDER)) {
            foreach ($matches as $match) {
                $fullMatch = $match[0];
                $altAttribute = $match[2];
                $styleAndRest = $match[3];
                
                // Only fix if it doesn't already have <img
                if (strpos($fullMatch, '<img') === false) {
                    $replacement = '<img src="{birthday_member_image}"' . $altAttribute . $styleAndRest;
                    $content = str_replace($fullMatch, $replacement, $content);
                    $hasChanges = true;
                    echo "<p style='color: green;'>✅ Fixed regex pattern: " . htmlspecialchars(substr($fullMatch, 0, 50)) . "...</p>\n";
                }
            }
        }
    }
    
    // Fix 4: Ensure all alt attributes use the correct placeholder
    $content = preg_replace('/alt="Sandra Stern"/', 'alt="{birthday_member_full_name}"', $content);
    if (preg_last_error() === PREG_NO_ERROR && $content !== $originalContent) {
        $hasChanges = true;
        echo "<p style='color: green;'>✅ Fixed alt attributes</p>\n";
    }
    
    // Fix 5: Clean up any double placeholders
    $content = str_replace('{{birthday_member_full_name}}', '{birthday_member_full_name}', $content);
    $content = str_replace('{{birthday_member_image}}', '{birthday_member_image}', $content);
    
    if ($hasChanges) {
        // Update the template
        $updateStmt = $pdo->prepare("UPDATE email_templates SET content = ? WHERE id = ?");
        $result = $updateStmt->execute([$content, $template['id']]);
        
        if ($result) {
            echo "<p style='color: green; font-weight: bold; font-size: 16px;'>✅ TEMPLATE FIXED AND UPDATED!</p>\n";
            $fixedCount++;
            
            // Show the fixed content around image areas
            if (preg_match_all('/<img[^>]*>/i', $content, $imgMatches)) {
                echo "<h5>✅ Fixed Image Tags:</h5>\n";
                foreach ($imgMatches[0] as $i => $imgTag) {
                    echo "<div style='background: #d4edda; padding: 10px; margin: 5px 0; border-left: 4px solid #28a745;'>\n";
                    echo "<code>" . htmlspecialchars($imgTag) . "</code>\n";
                    echo "</div>\n";
                }
            }
            
            // Verify no more Sandra Stern
            if (strpos($content, 'Sandra Stern') === false) {
                echo "<p style='color: green; font-weight: bold;'>✅ Confirmed: No more 'Sandra Stern' in template!</p>\n";
            } else {
                echo "<p style='color: red; font-weight: bold;'>❌ Warning: 'Sandra Stern' still found in template!</p>\n";
            }
            
        } else {
            echo "<p style='color: red; font-weight: bold;'>❌ FAILED TO UPDATE TEMPLATE!</p>\n";
        }
    } else {
        echo "<p style='color: blue;'>ℹ️ No corruption found in this template.</p>\n";
    }
    
    echo "</div>\n";
}

// Final verification
echo "<div style='border: 3px solid #28a745; margin: 20px 0; padding: 20px; background: #f8fff9;'>\n";
echo "<h3>🔍 FINAL VERIFICATION</h3>\n";

// Check if any templates still contain Sandra Stern
$stmt = $pdo->prepare("SELECT COUNT(*) as count FROM email_templates WHERE content LIKE '%Sandra Stern%'");
$stmt->execute();
$stillCorrupted = $stmt->fetch()['count'];

if ($stillCorrupted == 0) {
    echo "<p style='color: green; font-weight: bold; font-size: 18px;'>🎉 SUCCESS! No templates contain 'Sandra Stern' anymore!</p>\n";
} else {
    echo "<p style='color: red; font-weight: bold; font-size: 18px;'>❌ WARNING: $stillCorrupted templates still contain 'Sandra Stern'!</p>\n";
}

echo "<p><strong>Templates processed:</strong> " . count($templates) . "</p>\n";
echo "<p><strong>Templates fixed:</strong> $fixedCount</p>\n";

if ($fixedCount > 0) {
    echo "<h4>🧪 TEST NOW:</h4>\n";
    echo "<p>Try the birthday notification again. You should see:</p>\n";
    echo "<ul>\n";
    echo "<li>✅ Proper member image with correct alt text</li>\n";
    echo "<li>✅ No more 'Sandra Stern' corruption</li>\n";
    echo "<li>✅ Proper HTML img tags</li>\n";
    echo "</ul>\n";
}

echo "</div>\n";

?>
