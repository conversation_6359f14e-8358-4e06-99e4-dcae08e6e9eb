<?php
/**
 * Test script to debug birthday image URL replacement issue
 */

require_once 'config.php';

echo "<h2>Birthday Image URL Replacement Debug Test</h2>\n";

try {
    // Get a member with an image for testing
    $stmt = $pdo->prepare("
        SELECT * FROM members 
        WHERE image_path IS NOT NULL AND image_path != '' 
        AND status = 'active'
        LIMIT 1
    ");
    $stmt->execute();
    $birthdayMember = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$birthdayMember) {
        echo "❌ No members with images found for testing\n";
        exit;
    }
    
    echo "<h3>Test Member: {$birthdayMember['full_name']}</h3>\n";
    echo "<p><strong>Image Path:</strong> {$birthdayMember['image_path']}</p>\n";
    
    // Get a birthday template
    $stmt = $pdo->prepare("
        SELECT * FROM email_templates 
        WHERE is_birthday_template = 1 
        LIMIT 1
    ");
    $stmt->execute();
    $template = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$template) {
        echo "❌ No birthday templates found\n";
        exit;
    }
    
    echo "<h3>Template: {$template['template_name']}</h3>\n";
    
    // Create member data as it would be in send_birthday_reminders.php
    $siteUrl = defined('SITE_URL') ? SITE_URL : 'http://localhost/campaign/church';
    $birthdayPhotoUrl = $siteUrl . '/' . ltrim($birthdayMember['image_path'], '/');
    
    $memberData = [
        'first_name' => 'Test',
        'full_name' => 'Test Recipient',
        'email' => '<EMAIL>',
        'birthday_member_name' => $birthdayMember['first_name'],
        'birthday_member_full_name' => $birthdayMember['full_name'],
        'birthday_member_photo_url' => $birthdayPhotoUrl,
        'birthday_member_image' => $birthdayPhotoUrl,
        'member_image' => $birthdayPhotoUrl,
        'member_image_url' => $birthdayPhotoUrl,
        '_original_image_path' => $birthdayMember['image_path'],
        '_is_birthday_notification' => true
    ];
    
    echo "<h3>Member Data URLs:</h3>\n";
    echo "<p><strong>birthday_member_photo_url:</strong> {$memberData['birthday_member_photo_url']}</p>\n";
    echo "<p><strong>_original_image_path:</strong> {$memberData['_original_image_path']}</p>\n";
    
    // Process template content with placeholders
    $content = $template['content'];
    $processedContent = replaceTemplatePlaceholders($content, $memberData);
    
    echo "<h3>Template Content Analysis:</h3>\n";
    
    // Extract image URLs from the processed content
    preg_match_all('/<img[^>]+src=["\']([^"\']+)["\'][^>]*>/i', $processedContent, $matches);
    
    if (!empty($matches[1])) {
        echo "<h4>Image URLs found in processed content:</h4>\n";
        foreach ($matches[1] as $i => $imgUrl) {
            echo "<p><strong>Image " . ($i + 1) . ":</strong> " . htmlspecialchars($imgUrl) . "</p>\n";
            
            // Check if this URL matches our birthday photo URL
            if ($imgUrl === $birthdayPhotoUrl) {
                echo "<span style='color: green;'>✅ This URL matches birthday_member_photo_url</span><br>\n";
            } else {
                echo "<span style='color: red;'>❌ This URL does NOT match birthday_member_photo_url</span><br>\n";
                echo "<span style='color: blue;'>Expected: " . htmlspecialchars($birthdayPhotoUrl) . "</span><br>\n";
            }
        }
    } else {
        echo "<p>❌ No image URLs found in processed content</p>\n";
    }
    
    echo "<h3>URL Replacement Test:</h3>\n";
    
    // Test the URL replacement logic from config.php
    $testBody = $processedContent;
    $originalBody = $testBody;
    
    // Simulate the replacement logic from config.php
    $testBody = str_replace($birthdayPhotoUrl, 'cid:test_cid', $testBody);
    
    if ($testBody !== $originalBody) {
        echo "<p style='color: green;'>✅ URL replacement would work - found and replaced birthday photo URL</p>\n";
        
        // Show what was replaced
        $diff = array_diff(explode(' ', $originalBody), explode(' ', $testBody));
        echo "<p><strong>Replacement successful!</strong></p>\n";
    } else {
        echo "<p style='color: red;'>❌ URL replacement would fail - birthday photo URL not found in content</p>\n";
        
        // Debug: show what we're looking for vs what's in the content
        echo "<h4>Debug Information:</h4>\n";
        echo "<p><strong>Looking for URL:</strong> " . htmlspecialchars($birthdayPhotoUrl) . "</p>\n";
        echo "<p><strong>Content contains:</strong></p>\n";
        echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd; max-height: 300px; overflow-y: auto;'>";
        echo htmlspecialchars($processedContent);
        echo "</pre>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}
?>
