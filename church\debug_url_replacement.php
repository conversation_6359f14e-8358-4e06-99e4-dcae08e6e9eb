<?php
/**
 * Debug script to see exactly what's happening with URL replacement
 */

require_once 'config.php';

echo "<h2>URL Replacement Debug</h2>\n";

try {
    // Get <PERSON> (the birthday member)
    $stmt = $pdo->prepare("SELECT * FROM members WHERE full_name LIKE '%Jennifer%' AND image_path IS NOT NULL LIMIT 1");
    $stmt->execute();
    $birthdayMember = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$birthdayMember) {
        echo "❌ Jennifer not found\n";
        exit;
    }
    
    // Get a notification template
    $notificationTemplate = getRandomTemplateForType('notification');
    
    if (!$notificationTemplate) {
        echo "❌ No notification template found\n";
        exit;
    }
    
    echo "<h3>Debug Info:</h3>\n";
    echo "<p><strong>Birthday Member:</strong> {$birthdayMember['full_name']}</p>\n";
    echo "<p><strong>Birthday Member Image:</strong> {$birthdayMember['image_path']}</p>\n";
    echo "<p><strong>Template:</strong> {$notificationTemplate['template_name']}</p>\n";
    
    // Create the URLs as they would be in the real system
    $siteUrl = defined('SITE_URL') ? SITE_URL : 'http://localhost/campaign/church';
    $birthdayPhotoUrl = $siteUrl . '/' . ltrim($birthdayMember['image_path'], '/');
    
    echo "<p><strong>Birthday Photo URL:</strong> " . htmlspecialchars($birthdayPhotoUrl) . "</p>\n";
    
    // Create member data as it would be in the real system
    $memberData = [
        'first_name' => 'Test',
        'full_name' => 'Test Recipient',
        'email' => '<EMAIL>',
        'birthday_member_name' => $birthdayMember['first_name'],
        'birthday_member_first_name' => $birthdayMember['first_name'],
        'birthday_member_full_name' => $birthdayMember['full_name'],
        'birthday_member_photo_url' => $birthdayPhotoUrl,
        'birthday_member_image' => $birthdayPhotoUrl,
        'member_image' => $birthdayPhotoUrl,
        'member_image_url' => $birthdayPhotoUrl,
        'image_path' => $birthdayPhotoUrl,
        'profile_photo' => $birthdayPhotoUrl,
        '_original_image_path' => $birthdayMember['image_path'],
        '_is_birthday_notification' => true
    ];
    
    // Process the template
    $content = replaceTemplatePlaceholders($notificationTemplate['content'], $memberData);
    
    echo "<h3>Template Processing:</h3>\n";
    echo "<h4>Original Template Content (first 500 chars):</h4>\n";
    echo "<pre style='background: #f9f9f9; padding: 10px; border: 1px solid #ddd; max-height: 200px; overflow-y: auto;'>";
    echo htmlspecialchars(substr($notificationTemplate['content'], 0, 500));
    echo "</pre>\n";
    
    echo "<h4>Processed Content (first 500 chars):</h4>\n";
    echo "<pre style='background: #f9f9f9; padding: 10px; border: 1px solid #ddd; max-height: 200px; overflow-y: auto;'>";
    echo htmlspecialchars(substr($content, 0, 500));
    echo "</pre>\n";
    
    // Extract image URLs from the processed content
    preg_match_all('/<img[^>]+src=["\']([^"\']+)["\'][^>]*>/i', $content, $matches);
    
    echo "<h3>Image URLs in Processed Content:</h3>\n";
    if (!empty($matches[1])) {
        foreach ($matches[1] as $i => $imgUrl) {
            echo "<p><strong>Image " . ($i + 1) . ":</strong> " . htmlspecialchars($imgUrl) . "</p>\n";
            
            // Check exact match
            if ($imgUrl === $birthdayPhotoUrl) {
                echo "<span style='color: green;'>✅ EXACT MATCH with birthday photo URL</span><br>\n";
            } else {
                echo "<span style='color: red;'>❌ NO MATCH with birthday photo URL</span><br>\n";
                echo "<span style='color: blue;'>Expected: " . htmlspecialchars($birthdayPhotoUrl) . "</span><br>\n";
                echo "<span style='color: orange;'>Found: " . htmlspecialchars($imgUrl) . "</span><br>\n";
                
                // Character-by-character comparison
                $expectedChars = str_split($birthdayPhotoUrl);
                $foundChars = str_split($imgUrl);
                $maxLen = max(count($expectedChars), count($foundChars));
                
                echo "<span style='color: purple;'>Character-by-character comparison:</span><br>\n";
                for ($j = 0; $j < min(50, $maxLen); $j++) {
                    $expectedChar = isset($expectedChars[$j]) ? $expectedChars[$j] : '(missing)';
                    $foundChar = isset($foundChars[$j]) ? $foundChars[$j] : '(missing)';
                    
                    if ($expectedChar !== $foundChar) {
                        echo "<span style='color: red;'>Position $j: Expected '$expectedChar', Found '$foundChar'</span><br>\n";
                        break;
                    }
                }
            }
            echo "<br>\n";
        }
    } else {
        echo "<p>❌ No image URLs found in processed content</p>\n";
    }
    
    // Test URL replacement step by step
    echo "<h3>URL Replacement Test:</h3>\n";
    
    $testBody = $content;
    $originalBody = $content;
    
    echo "<p><strong>Original body contains birthday photo URL:</strong> " . (strpos($testBody, $birthdayPhotoUrl) !== false ? 'YES' : 'NO') . "</p>\n";
    
    if (strpos($testBody, $birthdayPhotoUrl) !== false) {
        echo "<p><strong>Position of birthday photo URL in body:</strong> " . strpos($testBody, $birthdayPhotoUrl) . "</p>\n";
        
        // Show context around the URL
        $pos = strpos($testBody, $birthdayPhotoUrl);
        $start = max(0, $pos - 50);
        $length = min(strlen($testBody) - $start, 200);
        $context = substr($testBody, $start, $length);
        
        echo "<h4>Context around the URL:</h4>\n";
        echo "<pre style='background: #f0f0f0; padding: 10px; border: 1px solid #ddd;'>";
        echo htmlspecialchars($context);
        echo "</pre>\n";
    }
    
    // Perform the replacement
    $uniqueCid = 'test_birthday_image_' . uniqid();
    $testBody = str_replace($birthdayPhotoUrl, 'cid:' . $uniqueCid, $testBody);
    
    echo "<p><strong>After replacement - body contains CID:</strong> " . (strpos($testBody, 'cid:' . $uniqueCid) !== false ? 'YES' : 'NO') . "</p>\n";
    
    if ($testBody !== $originalBody) {
        echo "<p style='color: green;'>✅ URL replacement WORKED - content was modified</p>\n";
        
        // Count how many replacements were made
        $replacementCount = substr_count($originalBody, $birthdayPhotoUrl);
        echo "<p><strong>Number of URLs replaced:</strong> $replacementCount</p>\n";
        
        // Show the replaced content
        if (strpos($testBody, 'cid:' . $uniqueCid) !== false) {
            $pos = strpos($testBody, 'cid:' . $uniqueCid);
            $start = max(0, $pos - 50);
            $length = min(strlen($testBody) - $start, 200);
            $context = substr($testBody, $start, $length);
            
            echo "<h4>Context around the replaced CID:</h4>\n";
            echo "<pre style='background: #e8f5e8; padding: 10px; border: 1px solid #4caf50;'>";
            echo htmlspecialchars($context);
            echo "</pre>\n";
        }
        
    } else {
        echo "<p style='color: red;'>❌ URL replacement FAILED - content was not modified</p>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}
?>
