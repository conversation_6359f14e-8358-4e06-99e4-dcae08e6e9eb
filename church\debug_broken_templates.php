<?php
require_once 'config.php';

echo "<h2>🔍 Deep Analysis: Broken Template Issue</h2>\n";

global $pdo;

// Get the specific problematic templates
$stmt = $pdo->prepare("
    SELECT id, template_name, content 
    FROM email_templates 
    WHERE template_name LIKE '%Member Upcoming Birthday Notification%'
    ORDER BY template_name
");
$stmt->execute();
$templates = $stmt->fetchAll(PDO::FETCH_ASSOC);

foreach ($templates as $template) {
    echo "<div style='border: 3px solid #dc3545; margin: 20px 0; padding: 20px; background: #fff5f5;'>\n";
    echo "<h3>🚨 " . htmlspecialchars($template['template_name']) . " (ID: {$template['id']})</h3>\n";
    
    $content = $template['content'];
    
    // Look for the specific broken pattern: text followed by image attributes
    echo "<h4>🔍 Searching for Broken Patterns:</h4>\n";
    
    // Pattern 1: Name followed by image attributes (the main issue)
    if (preg_match_all('/([A-Za-z\s]+)"[^>]*alt="[^"]*"[^>]*style="[^"]*"[^>]*>/', $content, $matches)) {
        echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; margin: 10px 0;'>\n";
        echo "<h5>❌ CRITICAL: Found broken image patterns!</h5>\n";
        foreach ($matches[0] as $i => $fullMatch) {
            $nameText = $matches[1][$i];
            echo "<p><strong>Broken Pattern " . ($i + 1) . ":</strong></p>\n";
            echo "<code style='background: #fff; padding: 5px; display: block; margin: 5px 0;'>" . htmlspecialchars($fullMatch) . "</code>\n";
            echo "<p><strong>Extracted Name:</strong> <span style='color: red;'>" . htmlspecialchars($nameText) . "</span></p>\n";
            
            // Show context around this pattern
            $pos = strpos($content, $fullMatch);
            if ($pos !== false) {
                $start = max(0, $pos - 100);
                $length = 300;
                $context = substr($content, $start, $length);
                echo "<p><strong>Context:</strong></p>\n";
                echo "<pre style='background: #fff3cd; padding: 10px; font-size: 12px; white-space: pre-wrap;'>" . htmlspecialchars($context) . "</pre>\n";
            }
            echo "<hr>\n";
        }
        echo "</div>\n";
    }
    
    // Pattern 2: Look for incomplete img tags
    if (preg_match_all('/<img[^>]*src="[^"]*"[^>]*>/', $content, $imgMatches)) {
        echo "<h5>🖼️ Found " . count($imgMatches[0]) . " img tags:</h5>\n";
        foreach ($imgMatches[0] as $i => $imgTag) {
            echo "<div style='background: #e2e3e5; padding: 10px; margin: 5px 0;'>\n";
            echo "<p><strong>Image " . ($i + 1) . ":</strong></p>\n";
            echo "<code>" . htmlspecialchars($imgTag) . "</code>\n";
            echo "</div>\n";
        }
    }
    
    // Pattern 3: Look for placeholder usage
    if (preg_match_all('/\{[^}]+\}/', $content, $placeholderMatches)) {
        echo "<h5>📝 Found " . count(array_unique($placeholderMatches[0])) . " unique placeholders:</h5>\n";
        $uniquePlaceholders = array_unique($placeholderMatches[0]);
        foreach ($uniquePlaceholders as $placeholder) {
            $isImagePlaceholder = strpos($placeholder, 'image') !== false || strpos($placeholder, 'photo') !== false;
            $color = $isImagePlaceholder ? 'green' : 'blue';
            echo "<span style='color: $color; margin-right: 10px;'>$placeholder</span>\n";
        }
        echo "<br><br>\n";
    }
    
    // Show the raw content for manual inspection
    echo "<h4>📄 Raw Template Content:</h4>\n";
    echo "<textarea style='width: 100%; height: 200px; font-family: monospace; font-size: 12px;'>" . htmlspecialchars($content) . "</textarea>\n";
    
    echo "</div>\n";
}

if (empty($templates)) {
    echo "<p style='color: red;'>❌ No templates found matching the pattern!</p>\n";
    
    // Let's search more broadly
    echo "<h3>🔍 Searching all templates for similar issues...</h3>\n";
    $stmt = $pdo->prepare("SELECT id, template_name FROM email_templates WHERE content LIKE '%alt=%' OR content LIKE '%style=%'");
    $stmt->execute();
    $allTemplates = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($allTemplates as $t) {
        echo "<p>- " . htmlspecialchars($t['template_name']) . " (ID: {$t['id']})</p>\n";
    }
}

echo "<h3>🎯 Analysis Summary</h3>\n";
echo "<p>The issue appears to be that templates contain broken HTML where:</p>\n";
echo "<ol>\n";
echo "<li>A member's name (like 'Sandra Stern') appears as text</li>\n";
echo "<li>Followed immediately by image attributes (alt, style, etc.)</li>\n";
echo "<li>But missing the proper &lt;img src='' tag structure</li>\n";
echo "</ol>\n";
echo "<p>This suggests the template was corrupted during editing or placeholder replacement.</p>\n";

?>
