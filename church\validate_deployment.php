<?php
echo "<h1>✅ DEPLOYMENT VALIDATION</h1>\n";
echo "<p><strong>Purpose:</strong> Validate that the system is working correctly after cleanup</p>\n";

try {
    require_once 'config.php';
    echo "<p style='color: green;'>✅ Config.php loaded successfully</p>\n";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Config.php error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    exit;
}

echo "<h2>🔧 ENVIRONMENT VALIDATION</h2>\n";

echo "<div style='background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6;'>\n";
echo "<h3>📋 Current Environment:</h3>\n";

if (defined('ENVIRONMENT')) {
    echo "<p><strong>Environment:</strong> " . htmlspecialchars(ENVIRONMENT) . "</p>\n";
} else {
    echo "<p style='color: orange;'>⚠️ ENVIRONMENT not defined</p>\n";
}

if (defined('SITE_URL')) {
    echo "<p><strong>SITE_URL:</strong> " . htmlspecialchars(SITE_URL) . "</p>\n";
} else {
    echo "<p style='color: red;'>❌ SITE_URL not defined</p>\n";
}

if (defined('BASE_URL')) {
    echo "<p><strong>BASE_URL:</strong> " . htmlspecialchars(BASE_URL) . "</p>\n";
} else {
    echo "<p style='color: red;'>❌ BASE_URL not defined</p>\n";
}

if (defined('ADMIN_URL')) {
    echo "<p><strong>ADMIN_URL:</strong> " . htmlspecialchars(ADMIN_URL) . "</p>\n";
} else {
    echo "<p style='color: red;'>❌ ADMIN_URL not defined</p>\n";
}

echo "</div>\n";

echo "<h2>🗄️ DATABASE VALIDATION</h2>\n";

try {
    global $pdo;
    
    if ($pdo) {
        echo "<p style='color: green;'>✅ Database connection successful</p>\n";
        
        // Test a simple query
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM members");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "<p style='color: green;'>✅ Database query successful - " . $result['count'] . " members found</p>\n";
        
    } else {
        echo "<p style='color: red;'>❌ Database connection failed</p>\n";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

echo "<h2>📁 FILE STRUCTURE VALIDATION</h2>\n";

$requiredFiles = [
    'config.php',
    'environment.php',
    'index.php',
    'admin/index.php',
    'admin/login.php',
    'admin/dashboard.php',
    'user/login.php',
    'user/dashboard.php',
    'cron/birthday_reminders.php',
    'cron/process_scheduled_emails.php',
    'includes/email_functions.php'
];

$missingFiles = [];
foreach ($requiredFiles as $file) {
    if (file_exists(__DIR__ . '/' . $file)) {
        echo "<p style='color: green;'>✅ $file</p>\n";
    } else {
        echo "<p style='color: red;'>❌ $file - MISSING</p>\n";
        $missingFiles[] = $file;
    }
}

echo "<h2>🔗 URL FUNCTION VALIDATION</h2>\n";

$urlFunctions = [
    'get_base_url' => function_exists('get_base_url'),
    'get_admin_url' => function_exists('get_admin_url'),
    'url_for' => function_exists('url_for'),
    'admin_url_for' => function_exists('admin_url_for')
];

foreach ($urlFunctions as $func => $exists) {
    if ($exists) {
        echo "<p style='color: green;'>✅ $func() - Available</p>\n";
        
        // Test the function
        try {
            if ($func === 'url_for') {
                $result = url_for('test');
            } elseif ($func === 'admin_url_for') {
                $result = admin_url_for('test');
            } else {
                $result = call_user_func($func);
            }
            echo "<p style='margin-left: 20px; color: blue;'>→ Result: " . htmlspecialchars($result) . "</p>\n";
        } catch (Exception $e) {
            echo "<p style='margin-left: 20px; color: red;'>→ Error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
        }
    } else {
        echo "<p style='color: red;'>❌ $func() - Missing</p>\n";
    }
}

echo "<h2>🧹 CLEANUP VALIDATION</h2>\n";

// Check for remaining test/debug files
$testPatterns = ['test_*.php', 'debug_*.php', 'fix_*.php', 'check_*.php'];
$foundTestFiles = [];

foreach ($testPatterns as $pattern) {
    $files = glob(__DIR__ . '/' . $pattern);
    foreach ($files as $file) {
        $foundTestFiles[] = basename($file);
    }
}

if (empty($foundTestFiles)) {
    echo "<p style='color: green;'>✅ No test/debug files found - Cleanup successful</p>\n";
} else {
    echo "<div style='background: #fff3cd; padding: 10px; border: 1px solid #ffeaa7;'>\n";
    echo "<h4>⚠️ Remaining test/debug files:</h4>\n";
    foreach ($foundTestFiles as $file) {
        echo "<p>• $file</p>\n";
    }
    echo "</div>\n";
}

echo "<h2>📊 OVERALL STATUS</h2>\n";

$issues = [];
if (!defined('SITE_URL')) $issues[] = "SITE_URL not defined";
if (!defined('BASE_URL')) $issues[] = "BASE_URL not defined";
if (!$pdo) $issues[] = "Database connection failed";
if (!empty($missingFiles)) $issues[] = count($missingFiles) . " required files missing";
if (!empty($foundTestFiles)) $issues[] = count($foundTestFiles) . " test files still present";

if (empty($issues)) {
    echo "<div style='background: #d4edda; padding: 20px; border: 1px solid #c3e6cb; border-radius: 5px;'>\n";
    echo "<h3 style='color: #155724;'>🎉 SYSTEM VALIDATION SUCCESSFUL!</h3>\n";
    echo "<p>✅ All core components are working correctly</p>\n";
    echo "<p>✅ Environment configuration is proper</p>\n";
    echo "<p>✅ Database connection is active</p>\n";
    echo "<p>✅ Required files are present</p>\n";
    echo "<p>✅ URL functions are working</p>\n";
    echo "<p>✅ Cleanup was successful</p>\n";
    echo "<p><strong>🚀 System is ready for production deployment!</strong></p>\n";
    echo "</div>\n";
} else {
    echo "<div style='background: #f8d7da; padding: 20px; border: 1px solid #f5c6cb; border-radius: 5px;'>\n";
    echo "<h3 style='color: #721c24;'>⚠️ VALIDATION ISSUES FOUND</h3>\n";
    foreach ($issues as $issue) {
        echo "<p>❌ $issue</p>\n";
    }
    echo "<p><strong>Please resolve these issues before deployment.</strong></p>\n";
    echo "</div>\n";
}

echo "<h2>🔗 QUICK ACCESS LINKS</h2>\n";

echo "<div style='background: #e7f3ff; padding: 15px; border: 1px solid #b3d9ff;'>\n";
echo "<h3>🎯 Test These URLs:</h3>\n";

$testUrls = [
    'Main Site' => defined('BASE_URL') ? BASE_URL : 'Not available',
    'Admin Panel' => defined('ADMIN_URL') ? ADMIN_URL : 'Not available',
    'User Dashboard' => defined('USER_URL') ? USER_URL : 'Not available'
];

foreach ($testUrls as $name => $url) {
    if ($url !== 'Not available') {
        echo "<p><strong>$name:</strong> <a href='$url' target='_blank'>$url</a></p>\n";
    } else {
        echo "<p><strong>$name:</strong> <span style='color: red;'>$url</span></p>\n";
    }
}
echo "</div>\n";

echo "<h2>📋 NEXT STEPS</h2>\n";

echo "<div style='background: #d1ecf1; padding: 20px; border: 1px solid #bee5eb; border-radius: 5px;'>\n";
echo "<h3>🚀 For Production Deployment:</h3>\n";
echo "<ol>\n";
echo "<li><strong>Update config.php:</strong> Set SITE_URL to https://freedomassemblydb.online</li>\n";
echo "<li><strong>Upload files:</strong> Upload the entire church directory</li>\n";
echo "<li><strong>Test URLs:</strong> Verify all links above work on production</li>\n";
echo "<li><strong>Configure cron jobs:</strong> Set up automated processes</li>\n";
echo "<li><strong>Monitor logs:</strong> Check for any issues</li>\n";
echo "</ol>\n";
echo "</div>\n";

?>
