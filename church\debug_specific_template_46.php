<?php
require_once 'config.php';

echo "<h2>🔍 DEBUG: Template ID 46 Specific Issue</h2>\n";

global $pdo;

// Get the exact template being previewed
$templateId = 46;
$stmt = $pdo->prepare("SELECT * FROM email_templates WHERE id = ?");
$stmt->execute([$templateId]);
$template = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$template) {
    echo "<p style='color: red;'>❌ Template ID $templateId not found!</p>\n";
    exit;
}

echo "<h3>📋 Template: " . htmlspecialchars($template['template_name']) . "</h3>\n";

$content = $template['content'];

echo "<h4>🔍 Raw Template Content:</h4>\n";
echo "<textarea style='width: 100%; height: 300px; font-family: monospace; font-size: 12px;'>" . htmlspecialchars($content) . "</textarea>\n";

echo "<h4>🔍 Searching for the Exact Issue:</h4>\n";

// Look for the pattern that would cause "<Sandra Stern" to appear
$patterns = [
    '/<\{birthday_member_full_name\}/' => 'Malformed placeholder with &lt;',
    '/<\{member_image\}/' => 'Malformed image placeholder with &lt;',
    '/\{birthday_member_full_name\}[^<]*alt="[^"]*"/' => 'Placeholder followed by alt attribute',
    '/\{member_image\}[^<]*alt="[^"]*"/' => 'Image placeholder followed by alt attribute',
    '/<[A-Za-z\s]+alt="[^"]*"/' => 'Text with alt attribute (missing img tag)',
    '/Sandra Stern[^<]*alt="[^"]*"/' => 'Hardcoded Sandra Stern with alt'
];

foreach ($patterns as $pattern => $description) {
    if (preg_match_all($pattern, $content, $matches)) {
        echo "<div style='background: #f8d7da; padding: 10px; margin: 10px 0; border-left: 4px solid #dc3545;'>\n";
        echo "<h5>❌ Found: $description</h5>\n";
        foreach ($matches[0] as $match) {
            echo "<p><code>" . htmlspecialchars($match) . "</code></p>\n";
            
            // Show context
            $pos = strpos($content, $match);
            if ($pos !== false) {
                $start = max(0, $pos - 50);
                $length = 150;
                $context = substr($content, $start, $length);
                echo "<p><strong>Context:</strong> <code>" . htmlspecialchars($context) . "</code></p>\n";
            }
        }
        echo "</div>\n";
    }
}

echo "<h4>🔧 Applying Specific Fixes:</h4>\n";

$originalContent = $content;
$hasChanges = false;

// Fix 1: Remove < before placeholders
$fixes = [
    '/<\{birthday_member_full_name\}/' => '{birthday_member_full_name}',
    '/<\{member_image\}/' => '{member_image}',
    '/<\{birthday_member_image\}/' => '{birthday_member_image}',
    '/<\{([^}]+)\}/' => '{$1}'
];

foreach ($fixes as $pattern => $replacement) {
    $newContent = preg_replace($pattern, $replacement, $content);
    if ($newContent !== $content) {
        echo "<p style='color: green;'>✅ Fixed pattern: $pattern</p>\n";
        $content = $newContent;
        $hasChanges = true;
    }
}

// Fix 2: Fix broken image structures
// Look for: {placeholder}" alt="..." style="..."
$brokenImagePattern = '/(\{[^}]+\})"(\s*alt="[^"]*")(\s*style="[^"]*"[^>]*>)/';
if (preg_match_all($brokenImagePattern, $content, $matches, PREG_SET_ORDER)) {
    echo "<p style='color: orange;'>🔍 Found broken image structures:</p>\n";
    foreach ($matches as $match) {
        $fullMatch = $match[0];
        $placeholder = $match[1];
        $altAttribute = $match[2];
        $styleAndRest = $match[3];
        
        echo "<p><strong>Broken:</strong> <code>" . htmlspecialchars($fullMatch) . "</code></p>\n";
        
        // Determine the correct replacement based on placeholder
        if (strpos($placeholder, 'image') !== false) {
            $replacement = '<img src="{birthday_member_image_url}"' . $altAttribute . $styleAndRest;
        } else {
            $replacement = '<img src="{birthday_member_image_url}" alt="' . $placeholder . '"' . $styleAndRest;
        }
        
        echo "<p><strong>Fixed:</strong> <code>" . htmlspecialchars($replacement) . "</code></p>\n";
        
        $content = str_replace($fullMatch, $replacement, $content);
        $hasChanges = true;
    }
}

// Fix 3: Fix any remaining broken patterns
$content = preg_replace('/([A-Za-z\s]+)"(\s*alt="[^"]*")(\s*style="[^"]*"[^>]*>)/', '<img src="{birthday_member_image_url}"$2$3', $content);
if ($content !== $originalContent) {
    echo "<p style='color: green;'>✅ Fixed remaining broken image patterns</p>\n";
    $hasChanges = true;
}

if ($hasChanges) {
    // Update the template
    $updateStmt = $pdo->prepare("UPDATE email_templates SET content = ? WHERE id = ?");
    $result = $updateStmt->execute([$content, $templateId]);
    
    if ($result) {
        echo "<div style='border: 3px solid #28a745; margin: 20px 0; padding: 20px; background: #f8fff9;'>\n";
        echo "<h3>✅ TEMPLATE FIXED!</h3>\n";
        echo "<p><strong>Template ID $templateId has been updated.</strong></p>\n";
        
        echo "<h4>📋 Before/After Comparison:</h4>\n";
        echo "<div style='display: grid; grid-template-columns: 1fr 1fr; gap: 20px;'>\n";
        echo "<div>\n";
        echo "<h5>❌ Before:</h5>\n";
        echo "<textarea style='width: 100%; height: 200px; font-family: monospace; font-size: 10px;'>" . htmlspecialchars($template['content']) . "</textarea>\n";
        echo "</div>\n";
        echo "<div>\n";
        echo "<h5>✅ After:</h5>\n";
        echo "<textarea style='width: 100%; height: 200px; font-family: monospace; font-size: 10px;'>" . htmlspecialchars($content) . "</textarea>\n";
        echo "</div>\n";
        echo "</div>\n";
        
        echo "<h4>🧪 Test Now:</h4>\n";
        echo "<p>1. <strong>Refresh the template preview</strong> (F5 or Ctrl+R)</p>\n";
        echo "<p>2. The image should now display correctly instead of '&lt;Sandra Stern'</p>\n";
        echo "<p>3. You should see Sandra Stern's actual photo</p>\n";
        
        echo "</div>\n";
    } else {
        echo "<p style='color: red; font-weight: bold;'>❌ Failed to update template!</p>\n";
    }
} else {
    echo "<p style='color: blue;'>ℹ️ No changes were made. The template might already be correct.</p>\n";
    
    echo "<h4>🔍 Additional Investigation:</h4>\n";
    echo "<p>Since no obvious issues were found in the template, the problem might be:</p>\n";
    echo "<ul>\n";
    echo "<li>In the preview system's placeholder replacement logic</li>\n";
    echo "<li>In the order of placeholder replacements</li>\n";
    echo "<li>In the image file path or accessibility</li>\n";
    echo "</ul>\n";
    
    // Test the preview logic manually
    echo "<h4>🧪 Manual Preview Test:</h4>\n";
    
    // Simulate what the preview system does
    $birthday_member = [
        'full_name' => 'Sandra Stern',
        'email' => '<EMAIL>',
        'image_path' => 'assets/img/default-avatar.png'
    ];
    
    $birthdayMemberImagePath = '../' . ltrim($birthday_member['image_path'], '/');
    $birthdayMemberImageHtml = '<img src="' . $birthdayMemberImagePath . '" alt="' .
        htmlspecialchars($birthday_member['full_name']) .
        '" style="width: 140px; height: 140px; border-radius: 50%; object-fit: cover; border: 5px solid #ff758c; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">';
    
    $replacements = [
        '{birthday_member_full_name}' => $birthday_member['full_name'],
        '{member_image}' => $birthdayMemberImageHtml,
        '{birthday_member_image}' => $birthdayMemberImageHtml,
        '{birthday_member_image_url}' => $birthdayMemberImagePath,
    ];
    
    echo "<p><strong>Test replacements:</strong></p>\n";
    foreach ($replacements as $placeholder => $value) {
        $displayValue = strlen($value) > 100 ? substr($value, 0, 100) . '...' : $value;
        echo "<p><code>$placeholder</code> → " . htmlspecialchars($displayValue) . "</p>\n";
    }
    
    $testContent = str_replace(array_keys($replacements), array_values($replacements), $content);
    
    echo "<h5>📄 Test Result:</h5>\n";
    if (strpos($testContent, '<Sandra Stern') !== false) {
        echo "<p style='color: red;'>❌ Still shows '&lt;Sandra Stern' after replacement</p>\n";
        
        // Find where it appears
        $pos = strpos($testContent, '<Sandra Stern');
        $start = max(0, $pos - 50);
        $length = 150;
        $context = substr($testContent, $start, $length);
        echo "<p><strong>Context:</strong> <code>" . htmlspecialchars($context) . "</code></p>\n";
    } else {
        echo "<p style='color: green;'>✅ No '&lt;Sandra Stern' found after replacement</p>\n";
    }
}

echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; margin: 20px 0;'>\n";
echo "<h4>🎯 Expected Fix:</h4>\n";
echo "<p>After this fix, the template preview should show:</p>\n";
echo "<ul>\n";
echo "<li>✅ Sandra Stern's actual image (circular photo)</li>\n";
echo "<li>✅ Proper alt text: 'Sandra Stern'</li>\n";
echo "<li>✅ No more '&lt;Sandra Stern' text</li>\n";
echo "</ul>\n";
echo "</div>\n";

?>
