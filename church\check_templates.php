<?php
require_once 'config.php';

echo "Checking birthday templates for image placeholders...\n\n";

$stmt = $pdo->prepare("
    SELECT id, template_name, content
    FROM email_templates
    WHERE template_name IN ('Birthday Reminder Template 1', 'Member Upcoming Birthday Notification 2')
    ORDER BY template_name
");
$stmt->execute();
$templates = $stmt->fetchAll(PDO::FETCH_ASSOC);

foreach ($templates as $template) {
    echo "Template: " . $template['template_name'] . "\n";
    echo "ID: " . $template['id'] . "\n";
    
    $hasBirthdayMemberImage = strpos($template['content'], '{birthday_member_image}') !== false;
    $hasMemberImage = strpos($template['content'], '{member_image}') !== false;
    
    echo "Has {birthday_member_image}: " . ($hasBirthdayMemberImage ? 'YES' : 'NO') . "\n";
    echo "Has {member_image}: " . ($hasMemberImage ? 'YES' : 'NO') . "\n";
    
    // Check for any image tags
    preg_match_all('/<img[^>]+src=["\']([^"\']+)["\'][^>]*>/i', $template['content'], $imgMatches);
    if (!empty($imgMatches[1])) {
        echo "Image sources found:\n";
        foreach ($imgMatches[1] as $src) {
            echo "  - " . $src . "\n";
        }
    } else {
        echo "No image tags found\n";
    }
    
    echo "\n" . str_repeat("-", 50) . "\n\n";
}
?>
