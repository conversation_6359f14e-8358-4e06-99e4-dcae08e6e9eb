<?php
/**
 * Final test to verify birthday notifications work correctly
 */

require_once 'config.php';
require_once 'send_birthday_reminders.php';

echo "<h2>Final Birthday Notification Test</h2>\n";

try {
    // Create a BirthdayReminder instance
    $reminder = new BirthdayReminder($pdo);
    
    // Get <PERSON> (the birthday member)
    $stmt = $pdo->prepare("SELECT * FROM members WHERE full_name LIKE '%Jennifer%' AND image_path IS NOT NULL LIMIT 1");
    $stmt->execute();
    $birthdayMember = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$birthdayMember) {
        echo "❌ Jennifer not found\n";
        exit;
    }
    
    echo "<h3>Test Setup:</h3>\n";
    echo "<p><strong>Birthday Member:</strong> {$birthdayMember['full_name']}</p>\n";
    echo "<p><strong>Birthday Member Image:</strong> {$birthdayMember['image_path']}</p>\n";
    
    // Send birthday notifications to a limited number of recipients for testing
    echo "<h3>Sending Birthday Notifications...</h3>\n";
    
    // Enable detailed logging
    error_log("=== FINAL BIRTHDAY NOTIFICATION TEST START ===");
    
    // Send notifications using the real system
    $result = $reminder->sendMemberBirthdayNotifications(
        $birthdayMember['id'], // birthdayMemberId
        null,                  // templateId (use random)
        0                      // daysUntilBirthday (0 for today)
    );
    
    error_log("=== FINAL BIRTHDAY NOTIFICATION TEST END ===");
    
    echo "<h3>Results:</h3>\n";
    if ($result) {
        echo "<p><strong>Total Sent:</strong> " . ($result['total_sent'] ?? 'Unknown') . "</p>\n";
        echo "<p><strong>Total Failed:</strong> " . ($result['total_failed'] ?? 'Unknown') . "</p>\n";
        
        if (isset($result['total_sent']) && $result['total_sent'] > 0) {
            echo "<p style='color: green;'>✅ Birthday notifications sent successfully!</p>\n";
        } else {
            echo "<p style='color: orange;'>⚠️ No notifications sent (this might be normal if no active members)</p>\n";
        }
    } else {
        echo "<p style='color: red;'>❌ Failed to send birthday notifications</p>\n";
    }
    
    // Check the latest birthday image embedding log
    echo "<h3>Latest Image Embedding Results:</h3>\n";
    $logContent = file_get_contents('logs/birthday_image_embedding.log');
    $logLines = explode("\n", $logContent);
    
    // Get the last few entries
    $latestEntries = array_slice($logLines, -25);
    $latestEntries = array_filter($latestEntries, function($line) {
        return !empty(trim($line));
    });
    
    echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd; max-height: 400px; overflow-y: auto;'>";
    echo htmlspecialchars(implode("\n", $latestEntries));
    echo "</pre>\n";
    
    // Count successful embeddings
    $successCount = 0;
    $urlReplacedYes = 0;
    $urlReplacedNo = 0;
    
    foreach ($latestEntries as $line) {
        if (strpos($line, 'SUCCESS: Embedded birthday member image') !== false) {
            $successCount++;
        }
        if (strpos($line, 'URL replaced in email body: YES') !== false) {
            $urlReplacedYes++;
        }
        if (strpos($line, 'URL replaced in email body: NO') !== false) {
            $urlReplacedNo++;
        }
    }
    
    echo "<h3>Summary:</h3>\n";
    echo "<p><strong>Successful Image Embeddings:</strong> $successCount</p>\n";
    echo "<p><strong>URL Replacements - YES:</strong> $urlReplacedYes</p>\n";
    echo "<p><strong>URL Replacements - NO:</strong> $urlReplacedNo</p>\n";
    
    if ($successCount > 0) {
        echo "<p style='color: green;'>✅ Images are being embedded successfully</p>\n";
        
        if ($urlReplacedYes > 0) {
            echo "<p style='color: green;'>✅ URL replacement is working correctly</p>\n";
            echo "<p><strong>🎉 BIRTHDAY IMAGE ISSUE IS FIXED! 🎉</strong></p>\n";
            echo "<p>Birthday member images should now display correctly in email clients instead of showing as empty circles.</p>\n";
        } else if ($urlReplacedNo > 0) {
            echo "<p style='color: orange;'>⚠️ URL replacement check shows NO, but images are embedded</p>\n";
            echo "<p>This might be a false negative in the check logic, but the images should still work.</p>\n";
        }
    } else {
        echo "<p style='color: red;'>❌ No image embeddings found</p>\n";
    }
    
    echo "<h3>Next Steps:</h3>\n";
    echo "<ul>\n";
    echo "<li>Check your email inbox to verify that Jennifer's image displays correctly</li>\n";
    echo "<li>The image should appear as a proper photo instead of an empty circle</li>\n";
    echo "<li>If you see the image correctly, the fix is successful!</li>\n";
    echo "</ul>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "<p>Stack trace:</p>\n";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>\n";
}
?>
