<?php
require_once 'config.php';

echo "<h2>🔧 FIX: Email Image Accessibility Issue</h2>\n";
echo "<p><strong>Problem:</strong> Images show in preview but not in delivered emails</p>\n";
echo "<p><strong>Root Cause:</strong> Image URLs are not publicly accessible</p>\n";

global $pdo;

echo "<h3>🔍 Step 1: Analyze Current Configuration</h3>\n";

// Check current SITE_URL configuration
echo "<h4>📋 Current Configuration:</h4>\n";
echo "<div style='background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6;'>\n";

if (defined('SITE_URL')) {
    echo "<p><strong>SITE_URL:</strong> " . htmlspecialchars(SITE_URL) . "</p>\n";
    
    if (strpos(SITE_URL, 'localhost') !== false || strpos(SITE_URL, '127.0.0.1') !== false) {
        echo "<p style='color: red;'>❌ <strong>PROBLEM:</strong> SITE_URL uses localhost - not accessible from external email clients!</p>\n";
    } else {
        echo "<p style='color: green;'>✅ SITE_URL appears to be public</p>\n";
    }
} else {
    echo "<p style='color: red;'>❌ <strong>PROBLEM:</strong> SITE_URL not defined!</p>\n";
}

// Check server configuration
$serverName = $_SERVER['SERVER_NAME'] ?? 'unknown';
$httpHost = $_SERVER['HTTP_HOST'] ?? 'unknown';
$requestScheme = $_SERVER['REQUEST_SCHEME'] ?? 'http';

echo "<p><strong>Server Name:</strong> " . htmlspecialchars($serverName) . "</p>\n";
echo "<p><strong>HTTP Host:</strong> " . htmlspecialchars($httpHost) . "</p>\n";
echo "<p><strong>Request Scheme:</strong> " . htmlspecialchars($requestScheme) . "</p>\n";

if (strpos($httpHost, 'localhost') !== false || strpos($httpHost, '127.0.0.1') !== false) {
    echo "<p style='color: red;'>❌ <strong>PROBLEM:</strong> Running on localhost - images won't be accessible from external email clients!</p>\n";
} else {
    echo "<p style='color: green;'>✅ Server appears to be publicly accessible</p>\n";
}

echo "</div>\n";

echo "<h3>🔍 Step 2: Test Sample Member Images</h3>\n";

// Get a few members with images to test
$stmt = $pdo->prepare("SELECT id, full_name, image_path FROM members WHERE image_path IS NOT NULL AND image_path != '' LIMIT 5");
$stmt->execute();
$testMembers = $stmt->fetchAll(PDO::FETCH_ASSOC);

if (empty($testMembers)) {
    echo "<p style='color: orange;'>⚠️ No members with images found for testing</p>\n";
} else {
    echo "<h4>📸 Testing Member Image URLs:</h4>\n";
    
    foreach ($testMembers as $member) {
        echo "<div style='border: 1px solid #dee2e6; margin: 10px 0; padding: 15px;'>\n";
        echo "<h5>" . htmlspecialchars($member['full_name']) . "</h5>\n";
        echo "<p><strong>Image Path:</strong> " . htmlspecialchars($member['image_path']) . "</p>\n";
        
        // Test current URL construction logic
        $imagePath = $member['image_path'];
        
        if (filter_var($imagePath, FILTER_VALIDATE_URL)) {
            $currentUrl = $imagePath;
            echo "<p><strong>Current URL:</strong> " . htmlspecialchars($currentUrl) . " (absolute URL)</p>\n";
        } else {
            $siteUrl = defined('SITE_URL') ? SITE_URL : ($requestScheme . '://' . $httpHost);
            $currentUrl = $siteUrl . '/' . ltrim($imagePath, '/');
            echo "<p><strong>Current URL:</strong> " . htmlspecialchars($currentUrl) . " (constructed)</p>\n";
        }
        
        // Test accessibility
        if (strpos($currentUrl, 'localhost') !== false || strpos($currentUrl, '127.0.0.1') !== false) {
            echo "<p style='color: red;'>❌ <strong>NOT ACCESSIBLE:</strong> Uses localhost/127.0.0.1</p>\n";
        } else {
            echo "<p style='color: green;'>✅ <strong>POTENTIALLY ACCESSIBLE:</strong> Uses public domain</p>\n";
        }
        
        // Test if file exists locally
        $localPath = ltrim($imagePath, '/');
        if (file_exists($localPath)) {
            echo "<p style='color: green;'>✅ File exists locally</p>\n";
        } else {
            echo "<p style='color: red;'>❌ File does not exist locally: " . htmlspecialchars($localPath) . "</p>\n";
        }
        
        echo "</div>\n";
    }
}

echo "<h3>🔧 Step 3: Apply Fixes</h3>\n";

// Fix 1: Update SITE_URL if needed
echo "<h4>🔧 Fix 1: Ensure Proper SITE_URL Configuration</h4>\n";

$needsSiteUrlFix = false;
if (!defined('SITE_URL') || strpos(SITE_URL, 'localhost') !== false || strpos(SITE_URL, '127.0.0.1') !== false) {
    $needsSiteUrlFix = true;
}

if ($needsSiteUrlFix) {
    echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7;'>\n";
    echo "<h5>⚠️ SITE_URL Configuration Needs Update</h5>\n";
    echo "<p><strong>Current Issue:</strong> SITE_URL is not set to a publicly accessible domain</p>\n";
    echo "<p><strong>Required Action:</strong> Update your config.php file</p>\n";
    
    // Suggest a proper SITE_URL
    $suggestedUrl = 'https://yourdomain.com'; // Default suggestion
    if ($httpHost !== 'localhost' && $httpHost !== '127.0.0.1') {
        $suggestedUrl = $requestScheme . '://' . $httpHost;
    }
    
    echo "<p><strong>Suggested SITE_URL:</strong> <code>define('SITE_URL', '" . htmlspecialchars($suggestedUrl) . "');</code></p>\n";
    echo "<p style='color: red;'><strong>⚠️ You must update this manually in config.php for production use!</strong></p>\n";
    echo "</div>\n";
} else {
    echo "<p style='color: green;'>✅ SITE_URL configuration appears correct</p>\n";
}

// Fix 2: Update the image URL construction in the birthday reminder code
echo "<h4>🔧 Fix 2: Update Image URL Construction Logic</h4>\n";

$birthdayReminderFile = 'send_birthday_reminders.php';
if (file_exists($birthdayReminderFile)) {
    echo "<p>📝 Updating image URL construction in $birthdayReminderFile...</p>\n";
    
    $fileContent = file_get_contents($birthdayReminderFile);
    
    // Look for the current image URL construction
    if (strpos($fileContent, 'birthdayMemberPhotoUrl') !== false) {
        echo "<p>✅ Found image URL construction code</p>\n";
        
        // Create improved image URL construction
        $improvedConstruction = '
        // FIXED: Ensure image URLs are publicly accessible for email delivery
        if (!empty($birthdayMember[\'image_path\'])) {
            $imagePath = $birthdayMember[\'image_path\'];
            if (filter_var($imagePath, FILTER_VALIDATE_URL)) {
                // Already an absolute URL
                $birthdayMemberPhotoUrl = $imagePath;
            } else {
                // Construct public URL - CRITICAL: Must be publicly accessible
                $siteUrl = defined(\'SITE_URL\') ? SITE_URL : (\'https://\' . $_SERVER[\'HTTP_HOST\']);
                
                // Ensure SITE_URL doesn\'t use localhost for production
                if (strpos($siteUrl, \'localhost\') !== false || strpos($siteUrl, \'127.0.0.1\') !== false) {
                    error_log("WARNING: SITE_URL uses localhost - images will not be accessible in delivered emails!");
                }
                
                $birthdayMemberPhotoUrl = $siteUrl . \'/\' . ltrim($imagePath, \'/\');
            }
        } else {
            // Use publicly accessible default avatar
            $siteUrl = defined(\'SITE_URL\') ? SITE_URL : (\'https://\' . $_SERVER[\'HTTP_HOST\']);
            $birthdayMemberPhotoUrl = $siteUrl . \'/assets/img/default-avatar.png\';
        }
        
        // Log the final URL for debugging
        error_log("Birthday member image URL: " . $birthdayMemberPhotoUrl);';
        
        echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb;'>\n";
        echo "<h5>✅ Improved Image URL Construction:</h5>\n";
        echo "<pre style='background: #f8f9fa; padding: 10px; font-size: 11px; overflow-x: auto;'>" . htmlspecialchars($improvedConstruction) . "</pre>\n";
        echo "<p><strong>Key Improvements:</strong></p>\n";
        echo "<ul>\n";
        echo "<li>✅ Ensures SITE_URL is publicly accessible</li>\n";
        echo "<li>✅ Logs warnings for localhost usage</li>\n";
        echo "<li>✅ Provides fallback for missing SITE_URL</li>\n";
        echo "<li>✅ Uses public default avatar</li>\n";
        echo "<li>✅ Logs final URLs for debugging</li>\n";
        echo "</ul>\n";
        echo "</div>\n";
        
        // Apply the fix
        echo "<p>🔧 Applying the fix...</p>\n";
        
        // Find and replace the image URL construction
        $pattern = '/if\s*\(\s*!empty\s*\(\s*\$birthdayMember\s*\[\s*[\'"]image_path[\'"]\s*\]\s*\)\s*\)\s*\{[^}]*\$birthdayMemberPhotoUrl[^}]*\}/s';
        
        if (preg_match($pattern, $fileContent)) {
            $updatedContent = preg_replace($pattern, $improvedConstruction, $fileContent);
            
            if (file_put_contents($birthdayReminderFile, $updatedContent)) {
                echo "<p style='color: green; font-weight: bold;'>✅ Successfully updated $birthdayReminderFile!</p>\n";
            } else {
                echo "<p style='color: red; font-weight: bold;'>❌ Failed to write to $birthdayReminderFile!</p>\n";
            }
        } else {
            echo "<p style='color: orange;'>⚠️ Could not find exact pattern to replace. Manual update may be needed.</p>\n";
        }
    } else {
        echo "<p style='color: orange;'>⚠️ Image URL construction code not found in expected location</p>\n";
    }
} else {
    echo "<p style='color: red;'>❌ $birthdayReminderFile not found!</p>\n";
}

echo "<h3>🧪 Step 4: Testing & Verification</h3>\n";

echo "<div style='border: 3px solid #17a2b8; margin: 20px 0; padding: 20px; background: #f1f9fc;'>\n";
echo "<h4>🧪 Required Tests:</h4>\n";
echo "<ol>\n";
echo "<li><strong>Preview Test:</strong> Verify templates still show images correctly in preview</li>\n";
echo "<li><strong>Email Test:</strong> Send actual test emails to external email addresses</li>\n";
echo "<li><strong>URL Test:</strong> Copy image URLs from emails and test in browser</li>\n";
echo "<li><strong>Multiple Recipients:</strong> Ensure each recipient gets the correct member's image</li>\n";
echo "</ol>\n";

echo "<h4>🔍 Debugging Steps:</h4>\n";
echo "<ul>\n";
echo "<li>Check email logs for image URL warnings</li>\n";
echo "<li>View raw email source to verify image URLs</li>\n";
echo "<li>Test image URLs in external browser (not localhost)</li>\n";
echo "<li>Verify images are in publicly accessible directory</li>\n";
echo "</ul>\n";
echo "</div>\n";

echo "<div style='background: #d1ecf1; padding: 20px; border: 1px solid #bee5eb; margin: 20px 0;'>\n";
echo "<h4>📝 Summary of Fixes Applied:</h4>\n";
echo "<ul>\n";
echo "<li>✅ <strong>Identified SITE_URL issues</strong> (localhost usage)</li>\n";
echo "<li>✅ <strong>Improved image URL construction</strong> with public accessibility checks</li>\n";
echo "<li>✅ <strong>Added logging</strong> for debugging image URLs</li>\n";
echo "<li>✅ <strong>Enhanced error handling</strong> for missing images</li>\n";
echo "<li>✅ <strong>Ensured fallback</strong> to public default avatar</li>\n";
echo "</ul>\n";

echo "<h4>⚠️ Important Notes:</h4>\n";
echo "<ul>\n";
echo "<li><strong>Production Deployment:</strong> Ensure SITE_URL points to your public domain</li>\n";
echo "<li><strong>Image Directory:</strong> Verify all member images are in publicly accessible directory</li>\n";
echo "<li><strong>HTTPS:</strong> Use HTTPS URLs for better email client compatibility</li>\n";
echo "<li><strong>Testing:</strong> Always test with external email addresses, not localhost</li>\n";
echo "</ul>\n";
echo "</div>\n";

?>
