<?php
require_once 'config.php';
require_once 'send_birthday_reminders.php';

echo "<h2>Testing Birthday Notification Fix</h2>\n";

// Use the global $pdo connection
global $pdo;
$db = $pdo;

// Get a test member with an image
$stmt = $db->prepare("SELECT * FROM members WHERE image_path IS NOT NULL AND image_path != '' LIMIT 1");
$stmt->execute();
$birthdayMember = $stmt->fetch();

if (!$birthdayMember) {
    echo "<p style='color: red;'>No members with images found for testing.</p>\n";
    exit;
}

echo "<h3>Birthday Member: " . htmlspecialchars($birthdayMember['full_name']) . "</h3>\n";
echo "<p>Image Path: " . htmlspecialchars($birthdayMember['image_path']) . "</p>\n";

// Get a different member to be the recipient
$stmt = $db->prepare("SELECT * FROM members WHERE id != ? LIMIT 1");
$stmt->execute([$birthdayMember['id']]);
$recipient = $stmt->fetch();

if (!$recipient) {
    echo "<p style='color: red;'>No other members found for testing.</p>\n";
    exit;
}

echo "<h3>Recipient: " . htmlspecialchars($recipient['full_name']) . "</h3>\n";

// Get a birthday template
$stmt = $db->prepare("SELECT * FROM birthday_templates LIMIT 1");
$stmt->execute();
$template = $stmt->fetch();

if (!$template) {
    echo "<p style='color: red;'>No birthday templates found.</p>\n";
    exit;
}

echo "<h3>Template: " . htmlspecialchars($template['name']) . "</h3>\n";

// Create a birthday reminder instance
$birthdayReminder = new BirthdayReminder($db);

echo "<h3>Testing Birthday Notification Sending</h3>\n";

try {
    // Test sending birthday notifications for today
    $result = $birthdayReminder->sendMemberBirthdayNotifications($birthdayMember['id'], $template['id'], 0);

    if ($result > 0) {
        echo "<p style='color: green;'>✅ Birthday notification sent successfully to $result recipients!</p>\n";
        echo "<p>Check your email to see if the image and alt text are correct.</p>\n";
    } else {
        echo "<p style='color: red;'>❌ Birthday notification sending failed or no recipients found.</p>\n";
    }

} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

echo "<h3>Expected Results:</h3>\n";
echo "<ul>\n";
echo "<li>✅ Image alt text should show: <strong>" . htmlspecialchars($birthdayMember['full_name']) . "</strong></li>\n";
echo "<li>✅ Image src should point to birthday member's image: <strong>" . htmlspecialchars($birthdayMember['image_path']) . "</strong></li>\n";
echo "<li>✅ Email should be about: <strong>" . htmlspecialchars($birthdayMember['full_name']) . "'s birthday</strong></li>\n";
echo "</ul>\n";

echo "<h3>Check the email logs for details about image processing.</h3>\n";

?>
