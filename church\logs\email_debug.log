[2025-07-17 01:05:38] Global sendEmail called for <PERSON> <<EMAIL>>
[2025-07-17 01:05:38] Email type: Regular
[2025-07-17 01:05:38] SMTP settings: Host=smtp.hostinger.com, Port=465, Username=<EMAIL>, Secure=ssl
[2025-07-17 01:05:38] Set Reply-To address: <EMAIL>
[2025-07-17 01:05:38] Processing HTML email with images
[2025-07-17 01:05:38] Found 1 images in email content
[2025-07-17 01:05:38] Image file not found: C:/xampp/htdocs/campaign/church/{birthday_member_image_url} (from URL: {birthday_member_image_url})
[2025-07-17 01:05:38] Attempting to send email to: <EMAIL> with subject: Celebrating <PERSON>'s Special Day with Freedom Assembly
[2025-07-17 01:05:39] SMTP Debug [2]: SERVER -> CLIENT: 220 ESMTP smtp.hostinger.com

[2025-07-17 01:05:39] SMTP Debug [1]: CLIENT -> SERVER: EHLO localhost

[2025-07-17 01:05:39] SMTP Debug [2]: SERVER -> CLIENT: 250-smtp.hostinger.com
250-PIPELINING
250-SIZE 48811212
250-ETRN
250-AUTH PLAIN LOGIN
250-ENHANCEDSTATUSCODES
250-8BITMIME
250-DSN
250 CHUNKING

[2025-07-17 01:05:39] SMTP Debug [1]: CLIENT -> SERVER: AUTH LOGIN

[2025-07-17 01:05:39] SMTP Debug [2]: SERVER -> CLIENT: 334 VXNlcm5hbWU6

[2025-07-17 01:05:39] SMTP Debug [1]: CLIENT -> SERVER: [credentials hidden]
[2025-07-17 01:05:39] SMTP Debug [2]: SERVER -> CLIENT: 334 UGFzc3dvcmQ6

[2025-07-17 01:05:39] SMTP Debug [1]: CLIENT -> SERVER: [credentials hidden]
[2025-07-17 01:05:39] SMTP Debug [2]: SERVER -> CLIENT: 235 2.7.0 Authentication successful

[2025-07-17 01:05:39] SMTP Debug [1]: CLIENT -> SERVER: MAIL FROM:<<EMAIL>>

[2025-07-17 01:05:40] SMTP Debug [2]: SERVER -> CLIENT: 250 2.1.0 Ok

[2025-07-17 01:05:40] SMTP Debug [1]: CLIENT -> SERVER: RCPT TO:<<EMAIL>>

[2025-07-17 01:05:40] SMTP Debug [2]: SERVER -> CLIENT: 250 2.1.5 Ok

[2025-07-17 01:05:40] SMTP Debug [1]: CLIENT -> SERVER: DATA

[2025-07-17 01:05:40] SMTP Debug [2]: SERVER -> CLIENT: 354 End data with <CR><LF>.<CR><LF>

[2025-07-17 01:05:40] SMTP Debug [1]: CLIENT -> SERVER: Date: Thu, 17 Jul 2025 01:05:38 +0200

[2025-07-17 01:05:40] SMTP Debug [1]: CLIENT -> SERVER: To: Jennifer Godson <<EMAIL>>

[2025-07-17 01:05:40] SMTP Debug [1]: CLIENT -> SERVER: From: Freedom Assembly Church <<EMAIL>>

[2025-07-17 01:05:40] SMTP Debug [1]: CLIENT -> SERVER: Reply-To: Freedom Assembly Church <<EMAIL>>

[2025-07-17 01:05:40] SMTP Debug [1]: CLIENT -> SERVER: Subject: Celebrating Jennifer Godson's Special Day with Freedom Assembly

[2025-07-17 01:05:40] SMTP Debug [1]: CLIENT -> SERVER: Message-ID: <HnqZfPu54Q2SuCNRpXGmPX8YRSLL5E8zsjCOltK1I@localhost>

[2025-07-17 01:05:40] SMTP Debug [1]: CLIENT -> SERVER: X-Mailer: PHPMailer 6.9.3 (https://github.com/PHPMailer/PHPMailer)

[2025-07-17 01:05:40] SMTP Debug [1]: CLIENT -> SERVER: MIME-Version: 1.0

[2025-07-17 01:05:40] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: text/html; charset=iso-8859-1

[2025-07-17 01:05:40] SMTP Debug [1]: CLIENT -> SERVER: Content-Transfer-Encoding: 8bit

[2025-07-17 01:05:40] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-17 01:05:40] SMTP Debug [1]: CLIENT -> SERVER: <div style="max-width: 600px; margin: 0 auto; font-family: 'Poppins', sans-serif; color: #333; background-color: #eef7ff; padding: 25px;">

[2025-07-17 01:05:40] SMTP Debug [1]: CLIENT -> SERVER:     <div style="text-align: center; background: linear-gradient(135deg, #4facfe, #00c6fb); padding: 30px; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.2);">

[2025-07-17 01:05:40] SMTP Debug [1]: CLIENT -> SERVER:         

[2025-07-17 01:05:40] SMTP Debug [1]: CLIENT -> SERVER:         <h1 style="color: #fff; font-size: 36px; margin-bottom: 15px;">🎉🎂 Happy Birthday, Jennifer Godson! 🎂🎉</h1>

[2025-07-17 01:05:40] SMTP Debug [1]: CLIENT -> SERVER:         

[2025-07-17 01:05:40] SMTP Debug [1]: CLIENT -> SERVER:         <<img src="<img src="<img src="{birthday_member_image_url}" alt="Jennifer Godson" style="width: 160px; height: 160px; border-radius: 50%; margin: 15px auto; display: block; object-fit: cover; border: 6px solid #fff; box-shadow: 0 4px 8px rgba(0,0,0,0.1);" />

[2025-07-17 01:05:40] SMTP Debug [1]: CLIENT -> SERVER:         

[2025-07-17 01:05:40] SMTP Debug [1]: CLIENT -> SERVER:         <div style="background-color: #ffffff; padding: 20px; border-radius: 15px; margin: 20px 0; box-shadow: 0 4px 10px rgba(0,0,0,0.1);">

[2025-07-17 01:05:40] SMTP Debug [1]: CLIENT -> SERVER:             <p style="font-size: 18px; line-height: 1.6; font-weight: bold; color: #2c3e50;">Dear Jennifer Godson,</p>

[2025-07-17 01:05:40] SMTP Debug [1]: CLIENT -> SERVER:             <p style="font-size: 16px; line-height: 1.6; color: #444;">

[2025-07-17 01:05:40] SMTP Debug [1]: CLIENT -> SERVER:                 🎈 On behalf of <strong>Freedom Assembly Church</strong>, we celebrate you today! 

[2025-07-17 01:05:40] SMTP Debug [1]: CLIENT -> SERVER:                 May your special day overflow with happiness, love, and divine favor. 💖🎊

[2025-07-17 01:05:40] SMTP Debug [1]: CLIENT -> SERVER:             </p>

[2025-07-17 01:05:40] SMTP Debug [1]: CLIENT -> SERVER:             <p style="font-size: 16px; line-height: 1.6; color: #444;">

[2025-07-17 01:05:40] SMTP Debug [1]: CLIENT -> SERVER:                 You are a cherished part of our family, and we pray that this year brings you closer to your destiny and showers you with abundant blessings. 🌟🙏

[2025-07-17 01:05:40] SMTP Debug [1]: CLIENT -> SERVER:             </p>

[2025-07-17 01:05:40] SMTP Debug [1]: CLIENT -> SERVER:         </div>

[2025-07-17 01:05:40] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-17 01:05:40] SMTP Debug [1]: CLIENT -> SERVER:         <div style="background-color: #eaf6ff; border-left: 5px solid #3498db; padding: 20px; border-radius: 12px; margin: 20px 0; box-shadow: 0 4px 10px rgba(0,0,0,0.1);">

[2025-07-17 01:05:40] SMTP Debug [1]: CLIENT -> SERVER:             <h2 style="color: #2980b9; font-size: 22px; margin-bottom: 12px;">💖 Birthday Blessing ✨</h2>

[2025-07-17 01:05:40] SMTP Debug [1]: CLIENT -> SERVER:             <p style="font-style: italic; color: #555; font-size: 16px; line-height: 1.6;">

[2025-07-17 01:05:40] SMTP Debug [1]: CLIENT -> SERVER:                 "May He give you the desire of your heart and make all your plans succeed." 

[2025-07-17 01:05:40] SMTP Debug [1]: CLIENT -> SERVER:                 <br><strong>- Psalm 20:4 🙏📖</strong>

[2025-07-17 01:05:40] SMTP Debug [1]: CLIENT -> SERVER:             </p>

[2025-07-17 01:05:40] SMTP Debug [1]: CLIENT -> SERVER:         </div>

[2025-07-17 01:05:40] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-17 01:05:40] SMTP Debug [1]: CLIENT -> SERVER:         <p style="font-size: 16px; line-height: 1.6; color: #2c3e50; margin: 20px 0;">

[2025-07-17 01:05:40] SMTP Debug [1]: CLIENT -> SERVER:             🌟 Wishing you a year filled with joy, success, and divine breakthroughs! Enjoy your special day! 🎊🎂🥳

[2025-07-17 01:05:40] SMTP Debug [1]: CLIENT -> SERVER:         </p>

[2025-07-17 01:05:40] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-17 01:05:40] SMTP Debug [1]: CLIENT -> SERVER:         <a href="{special_birthday_gift_link}" style="display: inline-block; background-color: #ff6b6b; color: #fff; padding: 12px 20px; border-radius: 8px; text-decoration: none; font-weight: bold; margin-top: 15px; box-shadow: 0 3px 8px rgba(0,0,0,0.2);">

[2025-07-17 01:05:40] SMTP Debug [1]: CLIENT -> SERVER:             🎁 Enjoy Your Day!

[2025-07-17 01:05:40] SMTP Debug [1]: CLIENT -> SERVER:         </a>

[2025-07-17 01:05:40] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-17 01:05:40] SMTP Debug [1]: CLIENT -> SERVER:         <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd;">

[2025-07-17 01:05:40] SMTP Debug [1]: CLIENT -> SERVER:             <p style="color: #666; font-size: 14px;">

[2025-07-17 01:05:40] SMTP Debug [1]: CLIENT -> SERVER:                 With Love & Prayers, <br>

[2025-07-17 01:05:40] SMTP Debug [1]: CLIENT -> SERVER:                 <strong>Freedom Assembly Church ❤️🙏</strong>

[2025-07-17 01:05:40] SMTP Debug [1]: CLIENT -> SERVER:             </p>

[2025-07-17 01:05:40] SMTP Debug [1]: CLIENT -> SERVER:         </div>

[2025-07-17 01:05:40] SMTP Debug [1]: CLIENT -> SERVER:     </div>

[2025-07-17 01:05:40] SMTP Debug [1]: CLIENT -> SERVER: </div>

[2025-07-17 01:05:40] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-17 01:05:40] SMTP Debug [1]: CLIENT -> SERVER: .

[2025-07-17 01:05:40] SMTP Debug [2]: SERVER -> CLIENT: 250 2.0.0 Ok: queued as 4bjBVW0TMqz2T22w

[2025-07-17 01:05:40] SMTP Debug [1]: CLIENT -> SERVER: RSET

[2025-07-17 01:05:40] SMTP Debug [2]: SERVER -> CLIENT: 250 2.0.0 Ok

[2025-07-17 01:05:40] Email sent <NAME_EMAIL>
[2025-07-17 01:05:40] SMTP Debug [1]: CLIENT -> SERVER: QUIT

[2025-07-17 01:05:41] SMTP Debug [2]: SERVER -> CLIENT: 221 2.0.0 Bye

[2025-07-17 01:13:00] Global sendEmail called for Jennifer Godson <<EMAIL>>
[2025-07-17 01:13:00] Email type: Regular
[2025-07-17 01:13:00] SMTP settings: Host=smtp.hostinger.com, Port=465, Username=<EMAIL>, Secure=ssl
[2025-07-17 01:13:00] Set Reply-To address: <EMAIL>
[2025-07-17 01:13:00] Processing HTML email with images
[2025-07-17 01:13:00] Found 1 images in email content
[2025-07-17 01:13:00] Image file not found: C:/xampp/htdocs/campaign/church/{birthday_member_image_url} (from URL: {birthday_member_image_url})
[2025-07-17 01:13:00] Attempting to send email to: <EMAIL> with subject: Happy Birthday Jennifer Godson! From Freedom Assembly Church
[2025-07-17 01:13:01] SMTP Debug [2]: SERVER -> CLIENT: 220 ESMTP smtp.hostinger.com

[2025-07-17 01:13:01] SMTP Debug [1]: CLIENT -> SERVER: EHLO localhost

[2025-07-17 01:13:01] SMTP Debug [2]: SERVER -> CLIENT: 250-smtp.hostinger.com
250-PIPELINING
250-SIZE 48811212
250-ETRN
250-AUTH PLAIN LOGIN
250-ENHANCEDSTATUSCODES
250-8BITMIME
250-DSN
250 CHUNKING

[2025-07-17 01:13:01] SMTP Debug [1]: CLIENT -> SERVER: AUTH LOGIN

[2025-07-17 01:13:01] SMTP Debug [2]: SERVER -> CLIENT: 334 VXNlcm5hbWU6

[2025-07-17 01:13:01] SMTP Debug [1]: CLIENT -> SERVER: [credentials hidden]
[2025-07-17 01:13:01] SMTP Debug [2]: SERVER -> CLIENT: 334 UGFzc3dvcmQ6

[2025-07-17 01:13:01] SMTP Debug [1]: CLIENT -> SERVER: [credentials hidden]
[2025-07-17 01:13:01] SMTP Debug [2]: SERVER -> CLIENT: 235 2.7.0 Authentication successful

[2025-07-17 01:13:01] SMTP Debug [1]: CLIENT -> SERVER: MAIL FROM:<<EMAIL>>

[2025-07-17 01:13:02] SMTP Debug [2]: SERVER -> CLIENT: 250 2.1.0 Ok

[2025-07-17 01:13:02] SMTP Debug [1]: CLIENT -> SERVER: RCPT TO:<<EMAIL>>

[2025-07-17 01:13:02] SMTP Debug [2]: SERVER -> CLIENT: 250 2.1.5 Ok

[2025-07-17 01:13:02] SMTP Debug [1]: CLIENT -> SERVER: DATA

[2025-07-17 01:13:02] SMTP Debug [2]: SERVER -> CLIENT: 354 End data with <CR><LF>.<CR><LF>

[2025-07-17 01:13:02] SMTP Debug [1]: CLIENT -> SERVER: Date: Thu, 17 Jul 2025 01:13:00 +0200

[2025-07-17 01:13:02] SMTP Debug [1]: CLIENT -> SERVER: To: Jennifer Godson <<EMAIL>>

[2025-07-17 01:13:02] SMTP Debug [1]: CLIENT -> SERVER: From: Freedom Assembly Church <<EMAIL>>

[2025-07-17 01:13:02] SMTP Debug [1]: CLIENT -> SERVER: Reply-To: Freedom Assembly Church <<EMAIL>>

[2025-07-17 01:13:02] SMTP Debug [1]: CLIENT -> SERVER: Subject: Happy Birthday Jennifer Godson! From Freedom Assembly Church

[2025-07-17 01:13:02] SMTP Debug [1]: CLIENT -> SERVER: Message-ID: <FGcxsKDGEjKVJzrn2BWRv4LEMEiumvXHgBpMy9HCI@localhost>

[2025-07-17 01:13:02] SMTP Debug [1]: CLIENT -> SERVER: X-Mailer: PHPMailer 6.9.3 (https://github.com/PHPMailer/PHPMailer)

[2025-07-17 01:13:02] SMTP Debug [1]: CLIENT -> SERVER: MIME-Version: 1.0

[2025-07-17 01:13:02] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: text/html; charset=iso-8859-1

[2025-07-17 01:13:02] SMTP Debug [1]: CLIENT -> SERVER: Content-Transfer-Encoding: 8bit

[2025-07-17 01:13:02] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-17 01:13:02] SMTP Debug [1]: CLIENT -> SERVER: <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333; background-color: #f4f7fc; padding: 25px;">

[2025-07-17 01:13:02] SMTP Debug [1]: CLIENT -> SERVER:     <div style="text-align: center; background-color: #ffffff; padding: 25px; border-radius: 12px; box-shadow: 0 5px 10px rgba(0,0,0,0.15);">

[2025-07-17 01:13:02] SMTP Debug [1]: CLIENT -> SERVER:         

[2025-07-17 01:13:02] SMTP Debug [1]: CLIENT -> SERVER:         <h1 style="color: #e67e22; font-size: 32px; margin-bottom: 15px;">🎉🎂 Happy Birthday, Jennifer Godson! 🎂🎉</h1>

[2025-07-17 01:13:02] SMTP Debug [1]: CLIENT -> SERVER:         

[2025-07-17 01:13:02] SMTP Debug [1]: CLIENT -> SERVER:         <<img src="<img src="<img src="{birthday_member_image_url}" alt="Jennifer Godson" style="width: 160px; height: 160px; border-radius: 50%; margin: 15px auto; display: block; object-fit: cover; border: 6px solid #fff; box-shadow: 0 4px 8px rgba(0,0,0,0.1);" />

[2025-07-17 01:13:02] SMTP Debug [1]: CLIENT -> SERVER:         

[2025-07-17 01:13:02] SMTP Debug [1]: CLIENT -> SERVER:         <div style="background-color: #ecf3fc; padding: 20px; border-radius: 12px; margin: 20px 0; border-left: 5px solid #3498db;">

[2025-07-17 01:13:02] SMTP Debug [1]: CLIENT -> SERVER:             <p style="font-size: 18px; line-height: 1.6; font-weight: bold; color: #2c3e50;">Dear Jennifer Godson,</p>

[2025-07-17 01:13:02] SMTP Debug [1]: CLIENT -> SERVER:             <p style="font-size: 16px; line-height: 1.6; color: #444;">

[2025-07-17 01:13:02] SMTP Debug [1]: CLIENT -> SERVER:                 On behalf of <strong style="color: #d35400;">Freedom Assembly Church</strong>, we celebrate you in 364 days on this special day! 🎈🎁  

[2025-07-17 01:13:02] SMTP Debug [1]: CLIENT -> SERVER:                 May your birthday be filled with joy, love, and divine blessings. 🙏✨

[2025-07-17 01:13:02] SMTP Debug [1]: CLIENT -> SERVER:             </p>

[2025-07-17 01:13:02] SMTP Debug [1]: CLIENT -> SERVER:             <p style="font-size: 16px; line-height: 1.6; color: #444;">

[2025-07-17 01:13:02] SMTP Debug [1]: CLIENT -> SERVER:                 You are a cherished part of our church family, and we pray that God’s grace and favor overflow in your life in this new year ahead. 🌟💖

[2025-07-17 01:13:02] SMTP Debug [1]: CLIENT -> SERVER:             </p>

[2025-07-17 01:13:02] SMTP Debug [1]: CLIENT -> SERVER:         </div>

[2025-07-17 01:13:02] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-17 01:13:02] SMTP Debug [1]: CLIENT -> SERVER:         <div style="background-color: #fffaf0; border-left: 5px solid #e67e22; padding: 20px; border-radius: 12px; margin: 20px 0;">

[2025-07-17 01:13:02] SMTP Debug [1]: CLIENT -> SERVER:             <h2 style="color: #d35400; font-size: 24px; margin-bottom: 12px;">📖 Birthday Scripture</h2>

[2025-07-17 01:13:02] SMTP Debug [1]: CLIENT -> SERVER:             <p style="font-style: italic; color: #555; font-size: 16px; line-height: 1.6;">

[2025-07-17 01:13:02] SMTP Debug [1]: CLIENT -> SERVER:                 "For I know the plans I have for you," declares the LORD,  

[2025-07-17 01:13:02] SMTP Debug [1]: CLIENT -> SERVER:                 "plans to prosper you and not to harm you, plans to give you hope and a future."  

[2025-07-17 01:13:02] SMTP Debug [1]: CLIENT -> SERVER:                 <br><strong style="color: #e74c3c;">- Jeremiah 29:11</strong> 🙏

[2025-07-17 01:13:02] SMTP Debug [1]: CLIENT -> SERVER:             </p>

[2025-07-17 01:13:02] SMTP Debug [1]: CLIENT -> SERVER:         </div>

[2025-07-17 01:13:02] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-17 01:13:02] SMTP Debug [1]: CLIENT -> SERVER:         <p style="font-size: 16px; line-height: 1.6; color: #2c3e50; margin: 20px 0;">

[2025-07-17 01:13:02] SMTP Debug [1]: CLIENT -> SERVER:             May God continue to bless and guide you abundantly! 🎊✨💝

[2025-07-17 01:13:02] SMTP Debug [1]: CLIENT -> SERVER:         </p>

[2025-07-17 01:13:02] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-17 01:13:02] SMTP Debug [1]: CLIENT -> SERVER:         <div style="margin-top: 30px; padding-top: 20px; border-top: 2px dashed #ddd;">

[2025-07-17 01:13:02] SMTP Debug [1]: CLIENT -> SERVER:             <p style="color: #666; font-size: 14px;">

[2025-07-17 01:13:02] SMTP Debug [1]: CLIENT -> SERVER:                 With Love & Prayers, 💕🙏<br>

[2025-07-17 01:13:02] SMTP Debug [1]: CLIENT -> SERVER:                 <strong style="color: #2980b9;">Freedom Assembly Church</strong>

[2025-07-17 01:13:02] SMTP Debug [1]: CLIENT -> SERVER:             </p>

[2025-07-17 01:13:02] SMTP Debug [1]: CLIENT -> SERVER:         </div>

[2025-07-17 01:13:02] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-17 01:13:02] SMTP Debug [1]: CLIENT -> SERVER:     </div>

[2025-07-17 01:13:02] SMTP Debug [1]: CLIENT -> SERVER: </div>

[2025-07-17 01:13:02] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-17 01:13:02] SMTP Debug [1]: CLIENT -> SERVER: .

[2025-07-17 01:13:02] SMTP Debug [2]: SERVER -> CLIENT: 250 2.0.0 Ok: queued as 4bjBg06zjtz5Z5qk

[2025-07-17 01:13:02] SMTP Debug [1]: CLIENT -> SERVER: RSET

[2025-07-17 01:13:03] SMTP Debug [2]: SERVER -> CLIENT: 250 2.0.0 Ok

[2025-07-17 01:13:03] Email sent <NAME_EMAIL>
[2025-07-17 01:13:03] SMTP Debug [1]: CLIENT -> SERVER: QUIT

[2025-07-17 01:13:03] SMTP Debug [2]: SERVER -> CLIENT: 221 2.0.0 Bye

[2025-07-17 01:14:25] Global sendEmail called for Godwin Bointa <<EMAIL>>
[2025-07-17 01:14:25] Email type: Regular
[2025-07-17 01:14:25] SMTP settings: Host=smtp.hostinger.com, Port=465, Username=<EMAIL>, Secure=ssl
[2025-07-17 01:14:25] Set Reply-To address: <EMAIL>
[2025-07-17 01:14:25] Processing HTML email with images
[2025-07-17 01:14:25] Found 1 images in email content
[2025-07-17 01:14:25] Image file not found: C:/xampp/htdocs/campaign/church/{birthday_member_image_url} (from URL: {birthday_member_image_url})
[2025-07-17 01:14:25] Attempting to send email to: <EMAIL> with subject: Happy Birthday, Godwin Bointa! Special Wishes For You
[2025-07-17 01:14:26] SMTP Debug [2]: SERVER -> CLIENT: 220 ESMTP smtp.hostinger.com

[2025-07-17 01:14:26] SMTP Debug [1]: CLIENT -> SERVER: EHLO localhost

[2025-07-17 01:14:26] SMTP Debug [2]: SERVER -> CLIENT: 250-smtp.hostinger.com
250-PIPELINING
250-SIZE 48811212
250-ETRN
250-AUTH PLAIN LOGIN
250-ENHANCEDSTATUSCODES
250-8BITMIME
250-DSN
250 CHUNKING

[2025-07-17 01:14:26] SMTP Debug [1]: CLIENT -> SERVER: AUTH LOGIN

[2025-07-17 01:14:26] SMTP Debug [2]: SERVER -> CLIENT: 334 VXNlcm5hbWU6

[2025-07-17 01:14:26] SMTP Debug [1]: CLIENT -> SERVER: [credentials hidden]
[2025-07-17 01:14:26] SMTP Debug [2]: SERVER -> CLIENT: 334 UGFzc3dvcmQ6

[2025-07-17 01:14:26] SMTP Debug [1]: CLIENT -> SERVER: [credentials hidden]
[2025-07-17 01:14:26] SMTP Debug [2]: SERVER -> CLIENT: 235 2.7.0 Authentication successful

[2025-07-17 01:14:26] SMTP Debug [1]: CLIENT -> SERVER: MAIL FROM:<<EMAIL>>

[2025-07-17 01:14:27] SMTP Debug [2]: SERVER -> CLIENT: 250 2.1.0 Ok

[2025-07-17 01:14:27] SMTP Debug [1]: CLIENT -> SERVER: RCPT TO:<<EMAIL>>

[2025-07-17 01:14:27] SMTP Debug [2]: SERVER -> CLIENT: 250 2.1.5 Ok

[2025-07-17 01:14:27] SMTP Debug [1]: CLIENT -> SERVER: DATA

[2025-07-17 01:14:27] SMTP Debug [2]: SERVER -> CLIENT: 354 End data with <CR><LF>.<CR><LF>

[2025-07-17 01:14:27] SMTP Debug [1]: CLIENT -> SERVER: Date: Thu, 17 Jul 2025 01:14:25 +0200

[2025-07-17 01:14:27] SMTP Debug [1]: CLIENT -> SERVER: To: Godwin Bointa <<EMAIL>>

[2025-07-17 01:14:27] SMTP Debug [1]: CLIENT -> SERVER: From: Freedom Assembly Church <<EMAIL>>

[2025-07-17 01:14:27] SMTP Debug [1]: CLIENT -> SERVER: Reply-To: Freedom Assembly Church <<EMAIL>>

[2025-07-17 01:14:27] SMTP Debug [1]: CLIENT -> SERVER: Subject: Happy Birthday, Godwin Bointa! Special Wishes For You

[2025-07-17 01:14:27] SMTP Debug [1]: CLIENT -> SERVER: Message-ID: <flx6UPHeq5Z5HnQPjyd1bXSTsCi9VTrvFfG9jRc6M@localhost>

[2025-07-17 01:14:27] SMTP Debug [1]: CLIENT -> SERVER: X-Mailer: PHPMailer 6.9.3 (https://github.com/PHPMailer/PHPMailer)

[2025-07-17 01:14:27] SMTP Debug [1]: CLIENT -> SERVER: MIME-Version: 1.0

[2025-07-17 01:14:27] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: text/html; charset=iso-8859-1

[2025-07-17 01:14:27] SMTP Debug [1]: CLIENT -> SERVER: Content-Transfer-Encoding: 8bit

[2025-07-17 01:14:27] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-17 01:14:27] SMTP Debug [1]: CLIENT -> SERVER: <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333; background-color: #f4f7fc; padding: 25px;">

[2025-07-17 01:14:27] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-17 01:14:27] SMTP Debug [1]: CLIENT -> SERVER:     <div style="text-align: center; background: linear-gradient(135deg, #ff9ff3, #6c5ce7); padding: 30px; border-radius: 15px; box-shadow: 0 5px 10px rgba(0,0,0,0.15);">

[2025-07-17 01:14:27] SMTP Debug [1]: CLIENT -> SERVER:         

[2025-07-17 01:14:27] SMTP Debug [1]: CLIENT -> SERVER:         <h1 style="color: #fff; font-size: 34px; margin-bottom: 15px;">🎉🎂 Happy Birthday! 🎈🎊</h1>

[2025-07-17 01:14:27] SMTP Debug [1]: CLIENT -> SERVER:         

[2025-07-17 01:14:27] SMTP Debug [1]: CLIENT -> SERVER:         <<img src="<img src="<img src="{birthday_member_image_url}" alt="Godwin Bointa" style="width: 160px; height: 160px; border-radius: 50%; margin: 15px auto; display: block; object-fit: cover; border: 6px solid #fff; box-shadow: 0 4px 8px rgba(0,0,0,0.1);" />

[2025-07-17 01:14:27] SMTP Debug [1]: CLIENT -> SERVER:         

[2025-07-17 01:14:27] SMTP Debug [1]: CLIENT -> SERVER:         <div style="background-color: #fff; padding: 20px; border-radius: 12px; margin: 20px 0;">

[2025-07-17 01:14:27] SMTP Debug [1]: CLIENT -> SERVER:             <p style="font-size: 20px; font-weight: bold; color: #2c3e50;">Dear Godwin Bointa,</p>

[2025-07-17 01:14:27] SMTP Debug [1]: CLIENT -> SERVER:             <p style="font-size: 17px; line-height: 1.6; color: #444;">

[2025-07-17 01:14:27] SMTP Debug [1]: CLIENT -> SERVER:                 🎊 On behalf of <strong style="color: #6c5ce7;">Freedom Assembly Church</strong>, we celebrate YOU today!  

[2025-07-17 01:14:27] SMTP Debug [1]: CLIENT -> SERVER:                 May your special day be filled with love, laughter, and countless blessings. 💖🎈

[2025-07-17 01:14:27] SMTP Debug [1]: CLIENT -> SERVER:             </p>

[2025-07-17 01:14:27] SMTP Debug [1]: CLIENT -> SERVER:             <p style="font-size: 17px; line-height: 1.6; color: #444;">

[2025-07-17 01:14:27] SMTP Debug [1]: CLIENT -> SERVER:                 You are a cherished part of our church family, and we pray that this new year of your life brings abundant joy, divine favor, and deeper purpose in Christ. 🙏✨

[2025-07-17 01:14:27] SMTP Debug [1]: CLIENT -> SERVER:             </p>

[2025-07-17 01:14:27] SMTP Debug [1]: CLIENT -> SERVER:         </div>

[2025-07-17 01:14:27] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-17 01:14:27] SMTP Debug [1]: CLIENT -> SERVER:         <div style="background-color: #fff7e6; border-left: 5px solid #f39c12; padding: 20px; border-radius: 10px; margin: 20px 0;">

[2025-07-17 01:14:27] SMTP Debug [1]: CLIENT -> SERVER:             <h2 style="color: #e67e22; font-size: 22px; margin-bottom: 12px;">📖 Birthday Scripture 🙏</h2>

[2025-07-17 01:14:27] SMTP Debug [1]: CLIENT -> SERVER:             <p style="font-style: italic; color: #555; font-size: 17px; line-height: 1.6;">

[2025-07-17 01:14:27] SMTP Debug [1]: CLIENT -> SERVER:                 "For I know the plans I have for you," declares the LORD,  

[2025-07-17 01:14:27] SMTP Debug [1]: CLIENT -> SERVER:                 "plans to prosper you and not to harm you, plans to give you hope and a future."  

[2025-07-17 01:14:27] SMTP Debug [1]: CLIENT -> SERVER:                 <br><strong style="color: #c0392b;">- Jeremiah 29:11 ✨</strong>

[2025-07-17 01:14:27] SMTP Debug [1]: CLIENT -> SERVER:             </p>

[2025-07-17 01:14:27] SMTP Debug [1]: CLIENT -> SERVER:         </div>

[2025-07-17 01:14:27] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-17 01:14:27] SMTP Debug [1]: CLIENT -> SERVER:         <p style="font-size: 17px; line-height: 1.6; color: #2c3e50; margin: 20px 0;">

[2025-07-17 01:14:27] SMTP Debug [1]: CLIENT -> SERVER:             🎁 May God continue to bless and guide you abundantly! Have a fantastic birthday celebration! 🎂🥳

[2025-07-17 01:14:27] SMTP Debug [1]: CLIENT -> SERVER:         </p>

[2025-07-17 01:14:27] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-17 01:14:27] SMTP Debug [1]: CLIENT -> SERVER:         <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd;">

[2025-07-17 01:14:27] SMTP Debug [1]: CLIENT -> SERVER:             <p style="color: #666; font-size: 15px;">

[2025-07-17 01:14:27] SMTP Debug [1]: CLIENT -> SERVER:                 With Love & Prayers, <br>

[2025-07-17 01:14:27] SMTP Debug [1]: CLIENT -> SERVER:                 <strong style="color: #6c5ce7;">Freedom Assembly Church 💒❤️</strong>

[2025-07-17 01:14:27] SMTP Debug [1]: CLIENT -> SERVER:             </p>

[2025-07-17 01:14:27] SMTP Debug [1]: CLIENT -> SERVER:         </div>

[2025-07-17 01:14:27] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-17 01:14:27] SMTP Debug [1]: CLIENT -> SERVER:     </div>

[2025-07-17 01:14:27] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-17 01:14:27] SMTP Debug [1]: CLIENT -> SERVER: </div>

[2025-07-17 01:14:27] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-17 01:14:27] SMTP Debug [1]: CLIENT -> SERVER: .

