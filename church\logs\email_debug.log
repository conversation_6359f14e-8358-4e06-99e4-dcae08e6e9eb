[2025-07-17 00:20:48] Global sendEmail called for Administrator <<EMAIL>>
[2025-07-17 00:20:48] Email type: Regular
[2025-07-17 00:20:48] SMTP settings: Host=smtp.hostinger.com, Port=465, Username=<EMAIL>, Secure=ssl
[2025-07-17 00:20:48] Set Reply-To address: <EMAIL>
[2025-07-17 00:20:48] Attempting to send email to: <EMAIL> with subject: Password Reset - Freedom Assembly Church Admin
[2025-07-17 00:20:48] SMTP Debug [2]: SERVER -> CLIENT: 220 ESMTP smtp.hostinger.com

[2025-07-17 00:20:48] SMTP Debug [1]: CLIENT -> SERVER: EHLO localhost

[2025-07-17 00:20:49] SMTP Debug [2]: SERVER -> CLIENT: 250-smtp.hostinger.com
250-PIPELINING
250-SIZE 48811212
250-ETR<PERSON>
250-AUTH PLAIN LOGIN
250-ENHANCEDSTATUSCODES
250-8BITMIME
250-DSN
250 CHUNKING

[2025-07-17 00:20:49] SMTP Debug [1]: CLIENT -> SERVER: AUTH LOGIN

[2025-07-17 00:20:49] SMTP Debug [2]: SERVER -> CLIENT: 334 VXNlcm5hbWU6

[2025-07-17 00:20:49] SMTP Debug [1]: CLIENT -> SERVER: [credentials hidden]
[2025-07-17 00:20:49] SMTP Debug [2]: SERVER -> CLIENT: 334 UGFzc3dvcmQ6

[2025-07-17 00:20:49] SMTP Debug [1]: CLIENT -> SERVER: [credentials hidden]
[2025-07-17 00:20:49] SMTP Debug [2]: SERVER -> CLIENT: 235 2.7.0 Authentication successful

[2025-07-17 00:20:49] SMTP Debug [1]: CLIENT -> SERVER: MAIL FROM:<<EMAIL>>

[2025-07-17 00:20:50] SMTP Debug [2]: SERVER -> CLIENT: 250 2.1.0 Ok

[2025-07-17 00:20:50] SMTP Debug [1]: CLIENT -> SERVER: RCPT TO:<<EMAIL>>

[2025-07-17 00:20:50] SMTP Debug [2]: SERVER -> CLIENT: 250 2.1.5 Ok

[2025-07-17 00:20:50] SMTP Debug [1]: CLIENT -> SERVER: DATA

[2025-07-17 00:20:50] SMTP Debug [2]: SERVER -> CLIENT: 354 End data with <CR><LF>.<CR><LF>

[2025-07-17 00:20:50] SMTP Debug [1]: CLIENT -> SERVER: Date: Thu, 17 Jul 2025 00:20:48 +0200

[2025-07-17 00:20:50] SMTP Debug [1]: CLIENT -> SERVER: To: Administrator <<EMAIL>>

[2025-07-17 00:20:50] SMTP Debug [1]: CLIENT -> SERVER: From: Freedom Assembly Church <<EMAIL>>

[2025-07-17 00:20:50] SMTP Debug [1]: CLIENT -> SERVER: Reply-To: Freedom Assembly Church <<EMAIL>>

[2025-07-17 00:20:50] SMTP Debug [1]: CLIENT -> SERVER: Subject: Password Reset - Freedom Assembly Church Admin

[2025-07-17 00:20:50] SMTP Debug [1]: CLIENT -> SERVER: Message-ID: <YUoQ6Bw9xyaS2ezGM7rEoFOGMRz6ROqRB8YOzQCDw@localhost>

[2025-07-17 00:20:50] SMTP Debug [1]: CLIENT -> SERVER: X-Mailer: PHPMailer 6.9.3 (https://github.com/PHPMailer/PHPMailer)

[2025-07-17 00:20:50] SMTP Debug [1]: CLIENT -> SERVER: MIME-Version: 1.0

[2025-07-17 00:20:50] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: text/html; charset=iso-8859-1

[2025-07-17 00:20:50] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-17 00:20:50] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-17 00:20:50] SMTP Debug [1]: CLIENT -> SERVER:             <html>

[2025-07-17 00:20:50] SMTP Debug [1]: CLIENT -> SERVER:             <body>

[2025-07-17 00:20:50] SMTP Debug [1]: CLIENT -> SERVER:                 <h2>Password Reset Request</h2>

[2025-07-17 00:20:50] SMTP Debug [1]: CLIENT -> SERVER:                 <p>Dear Administrator,</p>

[2025-07-17 00:20:50] SMTP Debug [1]: CLIENT -> SERVER:                 <p>We received a request to reset your password for the Freedom Assembly Church Admin Portal.</p>

[2025-07-17 00:20:50] SMTP Debug [1]: CLIENT -> SERVER:                 <p>To reset your password, please click the link below:</p>

[2025-07-17 00:20:50] SMTP Debug [1]: CLIENT -> SERVER:                 <p><a href='http://localhost/campaign/church/admin/reset_password.php?token=d5da991dcf939b96b73fb4abbdafc6a2'>Reset Password</a></p>

[2025-07-17 00:20:50] SMTP Debug [1]: CLIENT -> SERVER:                 <p>This link will expire in 1 hour.</p>

[2025-07-17 00:20:50] SMTP Debug [1]: CLIENT -> SERVER:                 <p>If you did not request a password reset, please ignore this email or contact an administrator.</p>

[2025-07-17 00:20:50] SMTP Debug [1]: CLIENT -> SERVER:                 <p>Thank you,<br>Freedom Assembly Church Admin Team</p>

[2025-07-17 00:20:50] SMTP Debug [1]: CLIENT -> SERVER:             </body>

[2025-07-17 00:20:50] SMTP Debug [1]: CLIENT -> SERVER:             </html>

[2025-07-17 00:20:50] SMTP Debug [1]: CLIENT -> SERVER:         

[2025-07-17 00:20:50] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-17 00:20:50] SMTP Debug [1]: CLIENT -> SERVER: .

[2025-07-17 00:20:51] SMTP Debug [2]: SERVER -> CLIENT: 250 2.0.0 Ok: queued as 4bj9Vm6mWsz5Z5mf

[2025-07-17 00:20:51] SMTP Debug [1]: CLIENT -> SERVER: RSET

[2025-07-17 00:20:51] SMTP Debug [2]: SERVER -> CLIENT: 250 2.0.0 Ok

[2025-07-17 00:20:51] Email sent <NAME_EMAIL>
[2025-07-17 00:20:51] SMTP Debug [1]: CLIENT -> SERVER: QUIT

[2025-07-17 00:20:51] SMTP Debug [2]: SERVER -> CLIENT: 221 2.0.0 Bye

[2025-07-17 00:23:03] Global sendEmail called for Jennifer Godson <<EMAIL>>
[2025-07-17 00:23:03] Email type: Regular
[2025-07-17 00:23:03] SMTP settings: Host=smtp.hostinger.com, Port=465, Username=<EMAIL>, Secure=ssl
[2025-07-17 00:23:03] Set Reply-To address: <EMAIL>
[2025-07-17 00:23:03] Processing HTML email with images
[2025-07-17 00:23:03] Attempting to send email to: <EMAIL> with subject: Password Reset Request
[2025-07-17 00:23:04] SMTP Debug [2]: SERVER -> CLIENT: 220 ESMTP smtp.hostinger.com

[2025-07-17 00:23:04] SMTP Debug [1]: CLIENT -> SERVER: EHLO localhost

[2025-07-17 00:23:04] SMTP Debug [2]: SERVER -> CLIENT: 250-smtp.hostinger.com
250-PIPELINING
250-SIZE 48811212
250-ETRN
250-AUTH PLAIN LOGIN
250-ENHANCEDSTATUSCODES
250-8BITMIME
250-DSN
250 CHUNKING

[2025-07-17 00:23:04] SMTP Debug [1]: CLIENT -> SERVER: AUTH LOGIN

[2025-07-17 00:23:04] SMTP Debug [2]: SERVER -> CLIENT: 334 VXNlcm5hbWU6

[2025-07-17 00:23:04] SMTP Debug [1]: CLIENT -> SERVER: [credentials hidden]
[2025-07-17 00:23:05] SMTP Debug [2]: SERVER -> CLIENT: 334 UGFzc3dvcmQ6

[2025-07-17 00:23:05] SMTP Debug [1]: CLIENT -> SERVER: [credentials hidden]
[2025-07-17 00:23:05] SMTP Debug [2]: SERVER -> CLIENT: 235 2.7.0 Authentication successful

[2025-07-17 00:23:05] SMTP Debug [1]: CLIENT -> SERVER: MAIL FROM:<<EMAIL>>

[2025-07-17 00:23:05] SMTP Debug [2]: SERVER -> CLIENT: 250 2.1.0 Ok

[2025-07-17 00:23:05] SMTP Debug [1]: CLIENT -> SERVER: RCPT TO:<<EMAIL>>

[2025-07-17 00:23:05] SMTP Debug [2]: SERVER -> CLIENT: 250 2.1.5 Ok

[2025-07-17 00:23:05] SMTP Debug [1]: CLIENT -> SERVER: DATA

[2025-07-17 00:23:05] SMTP Debug [2]: SERVER -> CLIENT: 354 End data with <CR><LF>.<CR><LF>

[2025-07-17 00:23:05] SMTP Debug [1]: CLIENT -> SERVER: Date: Thu, 17 Jul 2025 00:23:03 +0200

[2025-07-17 00:23:05] SMTP Debug [1]: CLIENT -> SERVER: To: Jennifer Godson <<EMAIL>>

[2025-07-17 00:23:05] SMTP Debug [1]: CLIENT -> SERVER: From: Freedom Assembly Church <<EMAIL>>

[2025-07-17 00:23:05] SMTP Debug [1]: CLIENT -> SERVER: Reply-To: Freedom Assembly Church <<EMAIL>>

[2025-07-17 00:23:05] SMTP Debug [1]: CLIENT -> SERVER: Subject: Password Reset Request

[2025-07-17 00:23:05] SMTP Debug [1]: CLIENT -> SERVER: Message-ID: <4o3Iyud8Do9wGBTm54S0tzkZKM6UOWpQbm9TY4Fr0@localhost>

[2025-07-17 00:23:05] SMTP Debug [1]: CLIENT -> SERVER: X-Mailer: PHPMailer 6.9.3 (https://github.com/PHPMailer/PHPMailer)

[2025-07-17 00:23:05] SMTP Debug [1]: CLIENT -> SERVER: MIME-Version: 1.0

[2025-07-17 00:23:05] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: text/html; charset=iso-8859-1

[2025-07-17 00:23:05] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-17 00:23:05] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-17 00:23:05] SMTP Debug [1]: CLIENT -> SERVER:                         <h2>Password Reset Request</h2>

[2025-07-17 00:23:05] SMTP Debug [1]: CLIENT -> SERVER:                         <p>Hello Jennifer Godson,</p>

[2025-07-17 00:23:05] SMTP Debug [1]: CLIENT -> SERVER:                         <p>You have requested to reset your password. Click the link below to reset your password:</p>

[2025-07-17 00:23:05] SMTP Debug [1]: CLIENT -> SERVER:                         <p><a href='http://localhost/campaign/church/user/reset_password.php?token=4c519709ccdd5c60914099dc254cb5a9' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Reset Password</a></p>

[2025-07-17 00:23:05] SMTP Debug [1]: CLIENT -> SERVER:                         <p>If you did not request this password reset, please ignore this email.</p>

[2025-07-17 00:23:05] SMTP Debug [1]: CLIENT -> SERVER:                         <p>This link will expire in 4 hours for security reasons.</p>

[2025-07-17 00:23:05] SMTP Debug [1]: CLIENT -> SERVER:                         <p>Best regards,<br>Church Management Team</p>

[2025-07-17 00:23:05] SMTP Debug [1]: CLIENT -> SERVER:                     

[2025-07-17 00:23:05] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-17 00:23:05] SMTP Debug [1]: CLIENT -> SERVER: .

[2025-07-17 00:23:06] SMTP Debug [2]: SERVER -> CLIENT: 250 2.0.0 Ok: queued as 4bj9YN25R7z5Z5mf

[2025-07-17 00:23:06] SMTP Debug [1]: CLIENT -> SERVER: RSET

[2025-07-17 00:23:06] SMTP Debug [2]: SERVER -> CLIENT: 250 2.0.0 Ok

[2025-07-17 00:23:06] Email sent <NAME_EMAIL>
[2025-07-17 00:23:06] SMTP Debug [1]: CLIENT -> SERVER: QUIT

[2025-07-17 00:23:06] SMTP Debug [2]: SERVER -> CLIENT: 221 2.0.0 Bye

[2025-07-17 00:52:03] Global sendEmail called for Jennifer Godson <<EMAIL>>
[2025-07-17 00:52:03] Email type: Regular
[2025-07-17 00:52:03] SMTP settings: Host=smtp.hostinger.com, Port=465, Username=<EMAIL>, Secure=ssl
[2025-07-17 00:52:03] Set Reply-To address: <EMAIL>
[2025-07-17 00:52:03] Processing HTML email with images
[2025-07-17 00:52:03] Attempting to send email to: <EMAIL> with subject: Password Reset Request
[2025-07-17 00:52:04] SMTP Debug [2]: SERVER -> CLIENT: 220 ESMTP smtp.hostinger.com

[2025-07-17 00:52:04] SMTP Debug [1]: CLIENT -> SERVER: EHLO localhost

[2025-07-17 00:52:05] SMTP Debug [2]: SERVER -> CLIENT: 250-smtp.hostinger.com
250-PIPELINING
250-SIZE 48811212
250-ETRN
250-AUTH PLAIN LOGIN
250-ENHANCEDSTATUSCODES
250-8BITMIME
250-DSN
250 CHUNKING

[2025-07-17 00:52:05] SMTP Debug [1]: CLIENT -> SERVER: AUTH LOGIN

[2025-07-17 00:52:05] SMTP Debug [2]: SERVER -> CLIENT: 334 VXNlcm5hbWU6

[2025-07-17 00:52:05] SMTP Debug [1]: CLIENT -> SERVER: [credentials hidden]
[2025-07-17 00:52:05] SMTP Debug [2]: SERVER -> CLIENT: 334 UGFzc3dvcmQ6

[2025-07-17 00:52:05] SMTP Debug [1]: CLIENT -> SERVER: [credentials hidden]
[2025-07-17 00:52:05] SMTP Debug [2]: SERVER -> CLIENT: 235 2.7.0 Authentication successful

[2025-07-17 00:52:05] SMTP Debug [1]: CLIENT -> SERVER: MAIL FROM:<<EMAIL>>

[2025-07-17 00:52:05] SMTP Debug [2]: SERVER -> CLIENT: 250 2.1.0 Ok

[2025-07-17 00:52:05] SMTP Debug [1]: CLIENT -> SERVER: RCPT TO:<<EMAIL>>

[2025-07-17 00:52:06] SMTP Debug [2]: SERVER -> CLIENT: 250 2.1.5 Ok

[2025-07-17 00:52:06] SMTP Debug [1]: CLIENT -> SERVER: DATA

[2025-07-17 00:52:06] SMTP Debug [2]: SERVER -> CLIENT: 354 End data with <CR><LF>.<CR><LF>

[2025-07-17 00:52:06] SMTP Debug [1]: CLIENT -> SERVER: Date: Thu, 17 Jul 2025 00:52:03 +0200

[2025-07-17 00:52:06] SMTP Debug [1]: CLIENT -> SERVER: To: Jennifer Godson <<EMAIL>>

[2025-07-17 00:52:06] SMTP Debug [1]: CLIENT -> SERVER: From: Freedom Assembly Church <<EMAIL>>

[2025-07-17 00:52:06] SMTP Debug [1]: CLIENT -> SERVER: Reply-To: Freedom Assembly Church <<EMAIL>>

[2025-07-17 00:52:06] SMTP Debug [1]: CLIENT -> SERVER: Subject: Password Reset Request

[2025-07-17 00:52:06] SMTP Debug [1]: CLIENT -> SERVER: Message-ID: <I9D9RIej2klnemLCh5iuHKG9Bd9RrrFJ1jdXbaJDp6c@localhost>

[2025-07-17 00:52:06] SMTP Debug [1]: CLIENT -> SERVER: X-Mailer: PHPMailer 6.9.3 (https://github.com/PHPMailer/PHPMailer)

[2025-07-17 00:52:06] SMTP Debug [1]: CLIENT -> SERVER: MIME-Version: 1.0

[2025-07-17 00:52:06] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: text/html; charset=iso-8859-1

[2025-07-17 00:52:06] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-17 00:52:06] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-17 00:52:06] SMTP Debug [1]: CLIENT -> SERVER:                         <h2>Password Reset Request</h2>

[2025-07-17 00:52:06] SMTP Debug [1]: CLIENT -> SERVER:                         <p>Hello Jennifer Godson,</p>

[2025-07-17 00:52:06] SMTP Debug [1]: CLIENT -> SERVER:                         <p>You have requested to reset your password. Click the link below to reset your password:</p>

[2025-07-17 00:52:06] SMTP Debug [1]: CLIENT -> SERVER:                         <p><a href='http://localhost/campaign/church/user/reset_password.php?token=f38789e1c9bb8745d09bbb60e59544ec' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Reset Password</a></p>

[2025-07-17 00:52:06] SMTP Debug [1]: CLIENT -> SERVER:                         <p>If you did not request this password reset, please ignore this email.</p>

[2025-07-17 00:52:06] SMTP Debug [1]: CLIENT -> SERVER:                         <p>This link will expire in 4 hours for security reasons.</p>

[2025-07-17 00:52:06] SMTP Debug [1]: CLIENT -> SERVER:                         <p>Best regards,<br>Church Management Team</p>

[2025-07-17 00:52:06] SMTP Debug [1]: CLIENT -> SERVER:                     

[2025-07-17 00:52:06] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-17 00:52:06] SMTP Debug [1]: CLIENT -> SERVER: .

[2025-07-17 00:52:06] SMTP Debug [2]: SERVER -> CLIENT: 250 2.0.0 Ok: queued as 4bjBBr5NY9z2Srx7

[2025-07-17 00:52:06] SMTP Debug [1]: CLIENT -> SERVER: RSET

[2025-07-17 00:52:06] SMTP Debug [2]: SERVER -> CLIENT: 250 2.0.0 Ok

[2025-07-17 00:52:06] Email sent <NAME_EMAIL>
[2025-07-17 00:52:06] SMTP Debug [1]: CLIENT -> SERVER: QUIT

[2025-07-17 00:52:06] SMTP Debug [2]: SERVER -> CLIENT: 221 2.0.0 Bye

[2025-07-17 00:58:22] Global sendEmail called for Administrator <<EMAIL>>
[2025-07-17 00:58:22] Email type: Regular
[2025-07-17 00:58:22] SMTP settings: Host=smtp.hostinger.com, Port=465, Username=<EMAIL>, Secure=ssl
[2025-07-17 00:58:22] Set Reply-To address: <EMAIL>
[2025-07-17 00:58:22] Attempting to send email to: <EMAIL> with subject: Password Reset - Freedom Assembly Church Admin
[2025-07-17 00:58:23] SMTP Debug [2]: SERVER -> CLIENT: 220 ESMTP smtp.hostinger.com

[2025-07-17 00:58:23] SMTP Debug [1]: CLIENT -> SERVER: EHLO localhost

[2025-07-17 00:58:23] SMTP Debug [2]: SERVER -> CLIENT: 250-smtp.hostinger.com
250-PIPELINING
250-SIZE 48811212
250-ETRN
250-AUTH PLAIN LOGIN
250-ENHANCEDSTATUSCODES
250-8BITMIME
250-DSN
250 CHUNKING

[2025-07-17 00:58:23] SMTP Debug [1]: CLIENT -> SERVER: AUTH LOGIN

[2025-07-17 00:58:24] SMTP Debug [2]: SERVER -> CLIENT: 334 VXNlcm5hbWU6

[2025-07-17 00:58:24] SMTP Debug [1]: CLIENT -> SERVER: [credentials hidden]
[2025-07-17 00:58:24] SMTP Debug [2]: SERVER -> CLIENT: 334 UGFzc3dvcmQ6

[2025-07-17 00:58:24] SMTP Debug [1]: CLIENT -> SERVER: [credentials hidden]
[2025-07-17 00:58:24] SMTP Debug [2]: SERVER -> CLIENT: 235 2.7.0 Authentication successful

[2025-07-17 00:58:24] SMTP Debug [1]: CLIENT -> SERVER: MAIL FROM:<<EMAIL>>

[2025-07-17 00:58:24] SMTP Debug [2]: SERVER -> CLIENT: 250 2.1.0 Ok

[2025-07-17 00:58:24] SMTP Debug [1]: CLIENT -> SERVER: RCPT TO:<<EMAIL>>

[2025-07-17 00:58:24] SMTP Debug [2]: SERVER -> CLIENT: 250 2.1.5 Ok

[2025-07-17 00:58:24] SMTP Debug [1]: CLIENT -> SERVER: DATA

[2025-07-17 00:58:25] SMTP Debug [2]: SERVER -> CLIENT: 354 End data with <CR><LF>.<CR><LF>

[2025-07-17 00:58:25] SMTP Debug [1]: CLIENT -> SERVER: Date: Thu, 17 Jul 2025 00:58:22 +0200

[2025-07-17 00:58:25] SMTP Debug [1]: CLIENT -> SERVER: To: Administrator <<EMAIL>>

[2025-07-17 00:58:25] SMTP Debug [1]: CLIENT -> SERVER: From: Freedom Assembly Church <<EMAIL>>

[2025-07-17 00:58:25] SMTP Debug [1]: CLIENT -> SERVER: Reply-To: Freedom Assembly Church <<EMAIL>>

[2025-07-17 00:58:25] SMTP Debug [1]: CLIENT -> SERVER: Subject: Password Reset - Freedom Assembly Church Admin

[2025-07-17 00:58:25] SMTP Debug [1]: CLIENT -> SERVER: Message-ID: <dC8MPsFDwEsysgjUdvTH0QMvUgvA6VxU8SojKCWH9g@localhost>

[2025-07-17 00:58:25] SMTP Debug [1]: CLIENT -> SERVER: X-Mailer: PHPMailer 6.9.3 (https://github.com/PHPMailer/PHPMailer)

[2025-07-17 00:58:25] SMTP Debug [1]: CLIENT -> SERVER: MIME-Version: 1.0

[2025-07-17 00:58:25] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: text/html; charset=iso-8859-1

[2025-07-17 00:58:25] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-17 00:58:25] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-17 00:58:25] SMTP Debug [1]: CLIENT -> SERVER:             <html>

[2025-07-17 00:58:25] SMTP Debug [1]: CLIENT -> SERVER:             <body>

[2025-07-17 00:58:25] SMTP Debug [1]: CLIENT -> SERVER:                 <h2>Password Reset Request</h2>

[2025-07-17 00:58:25] SMTP Debug [1]: CLIENT -> SERVER:                 <p>Dear Administrator,</p>

[2025-07-17 00:58:25] SMTP Debug [1]: CLIENT -> SERVER:                 <p>We received a request to reset your password for the Freedom Assembly Church Admin Portal.</p>

[2025-07-17 00:58:25] SMTP Debug [1]: CLIENT -> SERVER:                 <p>To reset your password, please click the link below:</p>

[2025-07-17 00:58:25] SMTP Debug [1]: CLIENT -> SERVER:                 <p><a href='http://localhost/campaign/church/admin/reset_password.php?token=5c50b7509d8f661f7a73edc5880bd68c'>Reset Password</a></p>

[2025-07-17 00:58:25] SMTP Debug [1]: CLIENT -> SERVER:                 <p>This link will expire in 1 hour.</p>

[2025-07-17 00:58:25] SMTP Debug [1]: CLIENT -> SERVER:                 <p>If you did not request a password reset, please ignore this email or contact an administrator.</p>

[2025-07-17 00:58:25] SMTP Debug [1]: CLIENT -> SERVER:                 <p>Thank you,<br>Freedom Assembly Church Admin Team</p>

[2025-07-17 00:58:25] SMTP Debug [1]: CLIENT -> SERVER:             </body>

[2025-07-17 00:58:25] SMTP Debug [1]: CLIENT -> SERVER:             </html>

[2025-07-17 00:58:25] SMTP Debug [1]: CLIENT -> SERVER:         

[2025-07-17 00:58:25] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-17 00:58:25] SMTP Debug [1]: CLIENT -> SERVER: .

[2025-07-17 00:58:25] SMTP Debug [2]: SERVER -> CLIENT: 250 2.0.0 Ok: queued as 4bjBL74GLlz5Z5mf

[2025-07-17 00:58:25] SMTP Debug [1]: CLIENT -> SERVER: RSET

