<?php
require_once 'config.php';

echo "<h2>🔧 DIRECT FIX: Unclosed Tag Issue</h2>\n";

global $pdo;

// Get template ID 46 directly
$templateId = 46;
$stmt = $pdo->prepare("SELECT * FROM email_templates WHERE id = ?");
$stmt->execute([$templateId]);
$template = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$template) {
    echo "<p style='color: red;'>❌ Template ID $templateId not found!</p>\n";
    exit;
}

echo "<h3>📋 Template: " . htmlspecialchars($template['template_name']) . "</h3>\n";

$content = $template['content'];

echo "<h4>🔍 Raw Content Analysis:</h4>\n";

// Show the raw content in a way that reveals hidden characters
echo "<div style='background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; margin: 10px 0;'>\n";
echo "<h5>Raw Template Content (first 1000 chars):</h5>\n";
echo "<pre style='white-space: pre-wrap; font-family: monospace; font-size: 11px; max-height: 300px; overflow-y: auto;'>";
echo htmlspecialchars(substr($content, 0, 1000));
echo "</pre>\n";
echo "</div>\n";

// Look specifically for the pattern causing "<Sandra Stern"
echo "<h4>🔍 Searching for Unclosed Tags:</h4>\n";

$issues = [];

// Pattern 1: < followed by { (unclosed tag with placeholder)
if (preg_match_all('/<\{[^}]*\}/', $content, $matches, PREG_OFFSET_CAPTURE)) {
    foreach ($matches[0] as $match) {
        $issues[] = [
            'type' => 'Unclosed tag with placeholder',
            'pattern' => $match[0],
            'position' => $match[1],
            'fix' => str_replace('<{', '{', $match[0])
        ];
    }
}

// Pattern 2: < followed by text and " (broken img tag)
if (preg_match_all('/<([A-Za-z\s]+)"/', $content, $matches, PREG_OFFSET_CAPTURE)) {
    foreach ($matches[0] as $i => $match) {
        $fullMatch = $match[0];
        $textPart = $matches[1][$i][0];
        
        // Skip valid HTML tags
        if (!preg_match('/^(img|div|p|span|h[1-6]|a|br|hr|strong|em|b|i)/i', $textPart)) {
            $issues[] = [
                'type' => 'Broken img tag (missing img src=)',
                'pattern' => $fullMatch,
                'position' => $match[1],
                'fix' => '<img src="{birthday_member_image_url}"'
            ];
        }
    }
}

// Pattern 3: Standalone < without proper closing
if (preg_match_all('/<(?![a-zA-Z\/!])/', $content, $matches, PREG_OFFSET_CAPTURE)) {
    foreach ($matches[0] as $match) {
        $issues[] = [
            'type' => 'Standalone < character',
            'pattern' => $match[0],
            'position' => $match[1],
            'fix' => '&lt;'
        ];
    }
}

if (empty($issues)) {
    echo "<p style='color: green;'>✅ No obvious unclosed tag issues found in patterns.</p>\n";
    
    // Let's look for the specific "Sandra Stern" context
    if (strpos($content, 'Sandra') !== false) {
        $pos = strpos($content, 'Sandra');
        $start = max(0, $pos - 100);
        $length = 300;
        $context = substr($content, $start, $length);
        
        echo "<h4>🔍 Context around 'Sandra':</h4>\n";
        echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7;'>\n";
        echo "<pre style='white-space: pre-wrap; font-family: monospace; font-size: 12px;'>" . htmlspecialchars($context) . "</pre>\n";
        echo "</div>\n";
        
        // Manual analysis of this context
        echo "<h4>🔧 Manual Fix Attempt:</h4>\n";
        
        // Look for specific broken patterns in this context
        $fixedContext = $context;
        
        // Fix 1: <{birthday_member_full_name} → {birthday_member_full_name}
        $fixedContext = preg_replace('/<\{birthday_member_full_name\}/', '{birthday_member_full_name}', $fixedContext);
        
        // Fix 2: Any pattern like <Sandra Stern" → <img src="..." alt="Sandra Stern"
        $fixedContext = preg_replace('/<Sandra Stern"/', '<img src="{birthday_member_image_url}" alt="Sandra Stern"', $fixedContext);
        
        // Fix 3: Any pattern like <{member_image} → {member_image}
        $fixedContext = preg_replace('/<\{member_image\}/', '{member_image}', $fixedContext);
        
        if ($fixedContext !== $context) {
            echo "<p style='color: green;'>✅ Found and can fix issues in context!</p>\n";
            echo "<div style='display: grid; grid-template-columns: 1fr 1fr; gap: 20px;'>\n";
            echo "<div>\n";
            echo "<h5>❌ Before:</h5>\n";
            echo "<pre style='background: #f8d7da; padding: 10px; font-size: 11px;'>" . htmlspecialchars($context) . "</pre>\n";
            echo "</div>\n";
            echo "<div>\n";
            echo "<h5>✅ After:</h5>\n";
            echo "<pre style='background: #d4edda; padding: 10px; font-size: 11px;'>" . htmlspecialchars($fixedContext) . "</pre>\n";
            echo "</div>\n";
            echo "</div>\n";
        }
    }
} else {
    echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb;'>\n";
    echo "<h5>❌ Found " . count($issues) . " issues:</h5>\n";
    foreach ($issues as $i => $issue) {
        echo "<div style='margin: 10px 0; padding: 10px; background: white; border-left: 4px solid #dc3545;'>\n";
        echo "<p><strong>Issue " . ($i + 1) . ":</strong> " . $issue['type'] . "</p>\n";
        echo "<p><strong>Pattern:</strong> <code>" . htmlspecialchars($issue['pattern']) . "</code></p>\n";
        echo "<p><strong>Position:</strong> " . $issue['position'] . "</p>\n";
        echo "<p><strong>Fix:</strong> <code>" . htmlspecialchars($issue['fix']) . "</code></p>\n";
        
        // Show context
        $start = max(0, $issue['position'] - 50);
        $length = 150;
        $context = substr($content, $start, $length);
        echo "<p><strong>Context:</strong></p>\n";
        echo "<pre style='background: #f8f9fa; padding: 5px; font-size: 10px;'>" . htmlspecialchars($context) . "</pre>\n";
        echo "</div>\n";
    }
    echo "</div>\n";
}

echo "<h3>🔧 APPLYING COMPREHENSIVE FIX</h3>\n";

$originalContent = $content;
$fixedContent = $content;

// Apply all the fixes
$fixes = [
    // Fix unclosed tags with placeholders
    '/<\{birthday_member_full_name\}/' => '{birthday_member_full_name}',
    '/<\{member_image\}/' => '{member_image}',
    '/<\{birthday_member_image\}/' => '{birthday_member_image}',
    '/<\{([^}]+)\}/' => '{$1}',
    
    // Fix broken image patterns
    '/<Sandra Stern"/' => '<img src="{birthday_member_image_url}" alt="Sandra Stern"',
    '/<Member"/' => '<img src="{birthday_member_image_url}" alt="Member"',
    
    // Fix any remaining broken patterns
    '/([A-Za-z\s]+)"(\s*alt="[^"]*")(\s*style="[^"]*"[^>]*>)/' => '<img src="{birthday_member_image_url}"$2$3',
    
    // Fix standalone < characters that aren't part of valid HTML
    '/<(?![a-zA-Z\/!])/' => '&lt;'
];

$changesApplied = [];

foreach ($fixes as $pattern => $replacement) {
    $newContent = preg_replace($pattern, $replacement, $fixedContent);
    if ($newContent !== $fixedContent) {
        $changesApplied[] = "Applied fix: $pattern → $replacement";
        $fixedContent = $newContent;
    }
}

if (!empty($changesApplied)) {
    echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb;'>\n";
    echo "<h4>✅ Changes Applied:</h4>\n";
    foreach ($changesApplied as $change) {
        echo "<p>• " . htmlspecialchars($change) . "</p>\n";
    }
    echo "</div>\n";
    
    // Update the database
    $updateStmt = $pdo->prepare("UPDATE email_templates SET content = ? WHERE id = ?");
    $result = $updateStmt->execute([$fixedContent, $templateId]);
    
    if ($result) {
        echo "<div style='border: 3px solid #28a745; margin: 20px 0; padding: 20px; background: #f8fff9;'>\n";
        echo "<h3>🎉 TEMPLATE FIXED SUCCESSFULLY!</h3>\n";
        echo "<p><strong>Template ID $templateId has been updated and fixed.</strong></p>\n";
        
        echo "<h4>📋 Before/After Comparison:</h4>\n";
        
        // Show specific area around the fix
        if (strpos($originalContent, 'Sandra') !== false) {
            $pos = strpos($originalContent, 'Sandra');
            $start = max(0, $pos - 100);
            $length = 300;
            
            $beforeContext = substr($originalContent, $start, $length);
            $afterContext = substr($fixedContent, $start, $length);
            
            echo "<div style='display: grid; grid-template-columns: 1fr 1fr; gap: 20px;'>\n";
            echo "<div>\n";
            echo "<h5>❌ Before (Broken):</h5>\n";
            echo "<pre style='background: #f8d7da; padding: 10px; font-size: 11px; max-height: 200px; overflow-y: auto;'>" . htmlspecialchars($beforeContext) . "</pre>\n";
            echo "</div>\n";
            echo "<div>\n";
            echo "<h5>✅ After (Fixed):</h5>\n";
            echo "<pre style='background: #d4edda; padding: 10px; font-size: 11px; max-height: 200px; overflow-y: auto;'>" . htmlspecialchars($afterContext) . "</pre>\n";
            echo "</div>\n";
            echo "</div>\n";
        }
        
        echo "<h4>🧪 IMMEDIATE TEST:</h4>\n";
        echo "<p style='font-size: 18px; color: #28a745;'><strong>✅ NOW REFRESH THE TEMPLATE PREVIEW!</strong></p>\n";
        echo "<p>1. Go back to the template preview page</p>\n";
        echo "<p>2. Press F5 or Ctrl+R to refresh</p>\n";
        echo "<p>3. You should now see Sandra Stern's image instead of '&lt;Sandra Stern'</p>\n";
        
        echo "</div>\n";
    } else {
        echo "<p style='color: red; font-weight: bold;'>❌ Failed to update template in database!</p>\n";
    }
} else {
    echo "<p style='color: blue;'>ℹ️ No changes were needed - template appears to be correct.</p>\n";
    echo "<p>The issue might be in the preview system or image file accessibility.</p>\n";
}

echo "<div style='background: #e7f3ff; padding: 15px; border: 1px solid #b3d9ff; margin: 20px 0;'>\n";
echo "<h4>🎯 Expected Result After Fix:</h4>\n";
echo "<p>Instead of seeing: <code>&lt;Sandra Stern</code></p>\n";
echo "<p>You should now see: <strong>✅ Sandra Stern's circular photo with proper styling</strong></p>\n";
echo "</div>\n";

?>
