<?php
require_once 'config.php';
require_once 'send_birthday_reminders.php';

echo "<h2>🧪 TEST: Email Image Accessibility Fix</h2>\n";
echo "<p><strong>Purpose:</strong> Verify that images will be accessible in delivered emails</p>\n";

global $pdo;

try {
    echo "<h3>🔍 Step 1: Configuration Analysis</h3>\n";
    
    // Check SITE_URL configuration
    echo "<div style='background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6;'>\n";
    echo "<h4>📋 Current Configuration:</h4>\n";
    
    if (defined('SITE_URL')) {
        echo "<p><strong>SITE_URL:</strong> " . htmlspecialchars(SITE_URL) . "</p>\n";
        
        if (strpos(SITE_URL, 'localhost') !== false || strpos(SITE_URL, '127.0.0.1') !== false) {
            echo "<div style='background: #f8d7da; padding: 10px; border: 1px solid #f5c6cb; margin: 10px 0;'>\n";
            echo "<p style='color: red; font-weight: bold;'>❌ CRITICAL ISSUE: SITE_URL uses localhost!</p>\n";
            echo "<p>Images will NOT be accessible in delivered emails!</p>\n";
            echo "<p><strong>Required Fix:</strong> Update SITE_URL to your public domain in config.php</p>\n";
            echo "</div>\n";
        } else {
            echo "<p style='color: green;'>✅ SITE_URL appears to be publicly accessible</p>\n";
        }
    } else {
        echo "<p style='color: red;'>❌ SITE_URL not defined - will use server host</p>\n";
    }
    
    $serverHost = $_SERVER['HTTP_HOST'] ?? 'unknown';
    echo "<p><strong>Server Host:</strong> " . htmlspecialchars($serverHost) . "</p>\n";
    
    if (strpos($serverHost, 'localhost') !== false || strpos($serverHost, '127.0.0.1') !== false) {
        echo "<div style='background: #fff3cd; padding: 10px; border: 1px solid #ffeaa7; margin: 10px 0;'>\n";
        echo "<p style='color: orange; font-weight: bold;'>⚠️ WARNING: Running on localhost!</p>\n";
        echo "<p>For production, deploy to a public server and update SITE_URL</p>\n";
        echo "</div>\n";
    }
    
    echo "</div>\n";
    
    echo "<h3>🧪 Step 2: Test Image URL Generation</h3>\n";
    
    // Get test members with images
    $stmt = $pdo->prepare("SELECT * FROM members WHERE image_path IS NOT NULL AND image_path != '' LIMIT 3");
    $stmt->execute();
    $testMembers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($testMembers)) {
        echo "<p style='color: orange;'>⚠️ No members with images found for testing</p>\n";
        
        // Create a test scenario with default avatar
        $testMembers = [[
            'id' => 999,
            'full_name' => 'Test Member',
            'image_path' => null
        ]];
    }
    
    echo "<h4>📸 Testing Image URL Generation:</h4>\n";
    
    $birthdayReminder = new BirthdayReminder($pdo);
    
    foreach ($testMembers as $member) {
        echo "<div style='border: 2px solid #007bff; margin: 15px 0; padding: 15px;'>\n";
        echo "<h5>👤 " . htmlspecialchars($member['full_name']) . "</h5>\n";
        echo "<p><strong>Image Path:</strong> " . htmlspecialchars($member['image_path'] ?? 'null') . "</p>\n";
        
        // Test the URL generation logic
        $imagePath = $member['image_path'];
        
        if (!empty($imagePath)) {
            if (filter_var($imagePath, FILTER_VALIDATE_URL)) {
                $generatedUrl = $imagePath;
                echo "<p><strong>Type:</strong> Absolute URL (used as-is)</p>\n";
            } else {
                $siteUrl = defined('SITE_URL') ? SITE_URL : ('https://' . $_SERVER['HTTP_HOST']);
                $generatedUrl = $siteUrl . '/' . ltrim($imagePath, '/');
                echo "<p><strong>Type:</strong> Relative path (constructed URL)</p>\n";
            }
        } else {
            $siteUrl = defined('SITE_URL') ? SITE_URL : ('https://' . $_SERVER['HTTP_HOST']);
            $generatedUrl = $siteUrl . '/assets/img/default-avatar.png';
            echo "<p><strong>Type:</strong> Default avatar</p>\n";
        }
        
        echo "<p><strong>Generated URL:</strong> " . htmlspecialchars($generatedUrl) . "</p>\n";
        
        // Test accessibility
        if (strpos($generatedUrl, 'localhost') !== false || strpos($generatedUrl, '127.0.0.1') !== false) {
            echo "<div style='background: #f8d7da; padding: 10px; border: 1px solid #f5c6cb;'>\n";
            echo "<p style='color: red; font-weight: bold;'>❌ NOT ACCESSIBLE from external email clients!</p>\n";
            echo "<p>Uses localhost/127.0.0.1 - only accessible from this machine</p>\n";
            echo "</div>\n";
        } else {
            echo "<div style='background: #d4edda; padding: 10px; border: 1px solid #c3e6cb;'>\n";
            echo "<p style='color: green; font-weight: bold;'>✅ POTENTIALLY ACCESSIBLE from external email clients</p>\n";
            echo "<p>Uses public domain - should work in delivered emails</p>\n";
            echo "</div>\n";
        }
        
        // Test if local file exists
        if (!empty($imagePath) && !filter_var($imagePath, FILTER_VALIDATE_URL)) {
            $localPath = ltrim($imagePath, '/');
            if (file_exists($localPath)) {
                echo "<p style='color: green;'>✅ Local file exists: " . htmlspecialchars($localPath) . "</p>\n";
            } else {
                echo "<p style='color: red;'>❌ Local file missing: " . htmlspecialchars($localPath) . "</p>\n";
            }
        }
        
        echo "</div>\n";
    }
    
    echo "<h3>🧪 Step 3: Test Template Processing</h3>\n";
    
    // Test actual template processing
    $stmt = $pdo->prepare("SELECT * FROM email_templates WHERE template_name LIKE '%Member Upcoming Birthday Notification%' LIMIT 1");
    $stmt->execute();
    $template = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($template && !empty($testMembers)) {
        echo "<h4>📧 Testing Template Processing:</h4>\n";
        
        $testMember = $testMembers[0];
        $recipient = [
            'id' => 888,
            'full_name' => 'Test Recipient',
            'first_name' => 'Test',
            'email' => '<EMAIL>'
        ];
        
        echo "<p><strong>Template:</strong> " . htmlspecialchars($template['template_name']) . "</p>\n";
        echo "<p><strong>Birthday Member:</strong> " . htmlspecialchars($testMember['full_name']) . "</p>\n";
        echo "<p><strong>Recipient:</strong> " . htmlspecialchars($recipient['full_name']) . "</p>\n";
        
        // Process the template
        $processedContent = $birthdayReminder->processBirthdayMemberTemplate(
            $template['content'], 
            $recipient, 
            $testMember, 
            3
        );
        
        // Extract image URLs from processed content
        if (preg_match_all('/<img[^>]*src="([^"]*)"[^>]*>/i', $processedContent, $matches)) {
            echo "<h5>🖼️ Images Found in Processed Template:</h5>\n";
            
            foreach ($matches[1] as $i => $imageUrl) {
                echo "<div style='background: #e7f3ff; padding: 10px; margin: 10px 0; border-left: 4px solid #007bff;'>\n";
                echo "<p><strong>Image " . ($i + 1) . ":</strong> " . htmlspecialchars($imageUrl) . "</p>\n";
                
                if (strpos($imageUrl, 'localhost') !== false || strpos($imageUrl, '127.0.0.1') !== false) {
                    echo "<p style='color: red; font-weight: bold;'>❌ Will NOT work in delivered emails (localhost)</p>\n";
                } else {
                    echo "<p style='color: green; font-weight: bold;'>✅ Should work in delivered emails (public URL)</p>\n";
                }
                echo "</div>\n";
            }
        } else {
            echo "<p style='color: orange;'>⚠️ No images found in processed template</p>\n";
        }
    }
    
    echo "<h3>📋 Step 4: Summary & Recommendations</h3>\n";
    
    echo "<div style='border: 3px solid #17a2b8; margin: 20px 0; padding: 20px; background: #f1f9fc;'>\n";
    echo "<h4>🎯 Test Results Summary:</h4>\n";
    
    $hasLocalhostIssues = false;
    if (defined('SITE_URL') && (strpos(SITE_URL, 'localhost') !== false || strpos(SITE_URL, '127.0.0.1') !== false)) {
        $hasLocalhostIssues = true;
    }
    if (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false) {
        $hasLocalhostIssues = true;
    }
    
    if ($hasLocalhostIssues) {
        echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb;'>\n";
        echo "<h5>❌ CRITICAL ISSUES FOUND:</h5>\n";
        echo "<ul>\n";
        echo "<li>Images use localhost URLs - will NOT be accessible in delivered emails</li>\n";
        echo "<li>Recipients will see broken images or wrong images</li>\n";
        echo "<li>Email clients cannot access localhost from external networks</li>\n";
        echo "</ul>\n";
        echo "</div>\n";
        
        echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7;'>\n";
        echo "<h5>🔧 REQUIRED FIXES:</h5>\n";
        echo "<ol>\n";
        echo "<li><strong>Deploy to public server:</strong> Move from localhost to a public domain</li>\n";
        echo "<li><strong>Update SITE_URL:</strong> Set to your public domain in config.php</li>\n";
        echo "<li><strong>Verify image directory:</strong> Ensure images are in publicly accessible folder</li>\n";
        echo "<li><strong>Test with external email:</strong> Send test emails to Gmail/Outlook</li>\n";
        echo "</ol>\n";
        echo "</div>\n";
    } else {
        echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb;'>\n";
        echo "<h5>✅ CONFIGURATION LOOKS GOOD:</h5>\n";
        echo "<ul>\n";
        echo "<li>Images use public URLs - should be accessible in delivered emails</li>\n";
        echo "<li>SITE_URL is properly configured</li>\n";
        echo "<li>Ready for production email delivery</li>\n";
        echo "</ul>\n";
        echo "</div>\n";
        
        echo "<div style='background: #e7f3ff; padding: 15px; border: 1px solid #b3d9ff;'>\n";
        echo "<h5>🧪 RECOMMENDED TESTS:</h5>\n";
        echo "<ol>\n";
        echo "<li><strong>Send test emails:</strong> To external email addresses (Gmail, Outlook)</li>\n";
        echo "<li><strong>Verify image display:</strong> Check that correct member images appear</li>\n";
        echo "<li><strong>Test multiple recipients:</strong> Ensure each gets the right image</li>\n";
        echo "<li><strong>Check email logs:</strong> Monitor for any image-related warnings</li>\n";
        echo "</ol>\n";
        echo "</div>\n";
    }
    
    echo "</div>\n";
    
    echo "<div style='background: #e7f3ff; padding: 15px; border: 1px solid #b3d9ff; margin: 20px 0;'>\n";
    echo "<h4>🔧 Applied Fixes:</h4>\n";
    echo "<ul>\n";
    echo "<li>✅ <strong>Enhanced URL validation:</strong> Checks for absolute vs relative URLs</li>\n";
    echo "<li>✅ <strong>Localhost detection:</strong> Warns when URLs won't work in emails</li>\n";
    echo "<li>✅ <strong>Public URL construction:</strong> Uses SITE_URL or server host</li>\n";
    echo "<li>✅ <strong>Improved logging:</strong> Tracks image URLs for debugging</li>\n";
    echo "<li>✅ <strong>Fallback handling:</strong> Proper default avatar URLs</li>\n";
    echo "</ul>\n";
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 20px; border: 1px solid #f5c6cb;'>\n";
    echo "<h4>❌ Error During Testing:</h4>\n";
    echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "<pre style='background: #f8f9fa; padding: 10px; font-size: 12px;'>" . htmlspecialchars($e->getTraceAsString()) . "</pre>\n";
    echo "</div>\n";
}

?>
