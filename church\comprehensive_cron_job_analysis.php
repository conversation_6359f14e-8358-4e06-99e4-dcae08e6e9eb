<?php
require_once 'config.php';

echo "<h1>🔍 COMPREHENSIVE CRON JOB ANALYSIS & SETUP</h1>\n";
echo "<p><strong>Purpose:</strong> Complete scan and configuration of all automated processes for online deployment</p>\n";

global $pdo;

try {
    echo "<h2>📋 IDENTIFIED CRON JOB REQUIREMENTS</h2>\n";
    
    $cronJobs = [
        [
            'name' => 'Birthday Reminders',
            'file' => 'cron/birthday_reminders.php',
            'purpose' => 'Send birthday emails and upcoming birthday notifications',
            'frequency' => 'Daily at 7:00 AM',
            'cron_syntax' => '0 7 * * *',
            'priority' => 'HIGH',
            'dependencies' => ['email_templates', 'members', 'automated_emails_settings']
        ],
        [
            'name' => 'Scheduled Email Processing',
            'file' => 'cron/process_scheduled_emails.php',
            'purpose' => 'Process email campaigns and scheduled bulk emails',
            'frequency' => 'Every 5 minutes',
            'cron_syntax' => '*/5 * * * *',
            'priority' => 'HIGH',
            'dependencies' => ['email_schedules', 'email_schedule_recipients', 'email_templates']
        ],
        [
            'name' => 'Email Queue Processing',
            'file' => 'cron/process_email_queue.php',
            'purpose' => 'Process queued emails with rate limiting',
            'frequency' => 'Every 5 minutes',
            'cron_syntax' => '*/5 * * * *',
            'priority' => 'HIGH',
            'dependencies' => ['email_queue']
        ],
        [
            'name' => 'Event Reminders',
            'file' => 'cron/event_reminders.php',
            'purpose' => 'Send reminder emails 24 hours before events',
            'frequency' => 'Daily at 8:00 AM',
            'cron_syntax' => '0 8 * * *',
            'priority' => 'MEDIUM',
            'dependencies' => ['events', 'event_rsvps', 'members']
        ],
        [
            'name' => 'Birthday Gift Processing',
            'file' => 'cron/process_birthday_gifts.php',
            'purpose' => 'Process scheduled birthday gifts and reminders',
            'frequency' => 'Daily at 9:00 AM',
            'cron_syntax' => '0 9 * * *',
            'priority' => 'MEDIUM',
            'dependencies' => ['birthday_gifts', 'members']
        ],
        [
            'name' => 'System Cleanup',
            'file' => 'cron/system_cleanup.php',
            'purpose' => 'Weekly maintenance: cleanup logs, optimize database',
            'frequency' => 'Weekly on Sunday at 2:00 AM',
            'cron_syntax' => '0 2 * * 0',
            'priority' => 'LOW',
            'dependencies' => ['email_logs', 'email_tracking', 'admin_activity_logs']
        ]
    ];
    
    echo "<div style='margin: 20px 0;'>\n";
    foreach ($cronJobs as $job) {
        $priorityColor = $job['priority'] === 'HIGH' ? '#dc3545' : ($job['priority'] === 'MEDIUM' ? '#ffc107' : '#28a745');
        
        echo "<div style='border: 2px solid $priorityColor; margin: 15px 0; padding: 15px; border-radius: 5px;'>\n";
        echo "<h3 style='color: $priorityColor; margin-top: 0;'>{$job['priority']} PRIORITY: {$job['name']}</h3>\n";
        echo "<p><strong>File:</strong> {$job['file']}</p>\n";
        echo "<p><strong>Purpose:</strong> {$job['purpose']}</p>\n";
        echo "<p><strong>Frequency:</strong> {$job['frequency']}</p>\n";
        echo "<p><strong>Cron Syntax:</strong> <code>{$job['cron_syntax']}</code></p>\n";
        echo "<p><strong>Dependencies:</strong> " . implode(', ', $job['dependencies']) . "</p>\n";
        echo "</div>\n";
    }
    echo "</div>\n";
    
    echo "<h2>🔍 CURRENT CONFIGURATION ANALYSIS</h2>\n";
    
    // Check if cron files exist and are properly configured
    echo "<h3>📁 File Existence Check</h3>\n";
    $cronDir = __DIR__ . '/cron/';
    $missingFiles = [];
    $existingFiles = [];
    
    foreach ($cronJobs as $job) {
        $filePath = $cronDir . basename($job['file']);
        if (file_exists($filePath)) {
            $existingFiles[] = $job['file'];
            echo "<p style='color: green;'>✅ {$job['file']} - EXISTS</p>\n";
        } else {
            $missingFiles[] = $job['file'];
            echo "<p style='color: red;'>❌ {$job['file']} - MISSING</p>\n";
        }
    }
    
    // Check database dependencies
    echo "<h3>🗄️ Database Dependencies Check</h3>\n";
    $requiredTables = [];
    foreach ($cronJobs as $job) {
        $requiredTables = array_merge($requiredTables, $job['dependencies']);
    }
    $requiredTables = array_unique($requiredTables);
    
    $missingTables = [];
    $existingTables = [];
    
    foreach ($requiredTables as $table) {
        try {
            $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
            if ($stmt->rowCount() > 0) {
                $existingTables[] = $table;
                echo "<p style='color: green;'>✅ Table '$table' - EXISTS</p>\n";
            } else {
                $missingTables[] = $table;
                echo "<p style='color: red;'>❌ Table '$table' - MISSING</p>\n";
            }
        } catch (Exception $e) {
            $missingTables[] = $table;
            echo "<p style='color: red;'>❌ Table '$table' - ERROR: {$e->getMessage()}</p>\n";
        }
    }
    
    // Check SITE_URL configuration
    echo "<h3>🌐 Site URL Configuration</h3>\n";
    if (defined('SITE_URL')) {
        echo "<p><strong>Current SITE_URL:</strong> " . htmlspecialchars(SITE_URL) . "</p>\n";
        
        if (strpos(SITE_URL, 'localhost') !== false || strpos(SITE_URL, '127.0.0.1') !== false) {
            echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; margin: 10px 0;'>\n";
            echo "<p style='color: red; font-weight: bold;'>❌ CRITICAL ISSUE: SITE_URL uses localhost!</p>\n";
            echo "<p>This will break cron jobs when deployed online. Update to your production domain.</p>\n";
            echo "</div>\n";
        } else {
            echo "<p style='color: green;'>✅ SITE_URL appears to be production-ready</p>\n";
        }
    } else {
        echo "<p style='color: red;'>❌ SITE_URL not defined in config.php</p>\n";
    }
    
    // Check cron key security
    echo "<h3>🔐 Security Configuration</h3>\n";
    $cronKey = 'fac_2024_secure_cron_8x9q2p5m';
    echo "<p><strong>Cron Key:</strong> " . htmlspecialchars($cronKey) . "</p>\n";
    echo "<p style='color: green;'>✅ Secure cron key is configured</p>\n";
    
    echo "<h2>🔧 PRODUCTION DEPLOYMENT SETUP</h2>\n";
    
    // Generate production-ready cron commands
    $siteUrl = defined('SITE_URL') ? SITE_URL : 'https://freedomassemblydb.online';
    $basePath = '/campaign/church'; // Adjust this based on your deployment path
    
    echo "<h3>📝 Complete Cron Job Commands for Production</h3>\n";
    echo "<p><strong>Base URL:</strong> $siteUrl$basePath</p>\n";
    echo "<p><strong>Instructions:</strong> Add these commands to your server's crontab</p>\n";
    
    echo "<div style='background: #f8f9fa; padding: 20px; border: 1px solid #dee2e6; margin: 20px 0;'>\n";
    echo "<h4>🚀 COPY THESE COMMANDS TO YOUR CRONTAB:</h4>\n";
    echo "<pre style='background: #343a40; color: #fff; padding: 15px; overflow-x: auto;'>";
    
    foreach ($cronJobs as $job) {
        $url = $siteUrl . $basePath . '/' . $job['file'] . '?cron_key=' . $cronKey;
        echo "# {$job['name']} - {$job['purpose']}\n";
        echo "{$job['cron_syntax']} wget -q -O /dev/null \"$url\"\n\n";
    }
    
    echo "</pre>\n";
    echo "</div>\n";
    
    echo "<h3>🏗️ Alternative Setup Methods</h3>\n";
    
    echo "<div class='accordion' id='setupMethods'>\n";
    
    // Method 1: cPanel
    echo "<div style='border: 1px solid #dee2e6; margin: 10px 0;'>\n";
    echo "<h4 style='background: #e9ecef; padding: 10px; margin: 0;'>Method 1: cPanel Cron Jobs</h4>\n";
    echo "<div style='padding: 15px;'>\n";
    echo "<ol>\n";
    echo "<li>Log into your cPanel</li>\n";
    echo "<li>Find and click 'Cron Jobs'</li>\n";
    echo "<li>For each cron job above, create a new cron job with:</li>\n";
    echo "<ul>\n";
    echo "<li><strong>Command:</strong> wget -q -O /dev/null \"[URL from above]\"</li>\n";
    echo "<li><strong>Timing:</strong> Use the cron syntax provided</li>\n";
    echo "</ul>\n";
    echo "</ol>\n";
    echo "</div>\n";
    echo "</div>\n";
    
    // Method 2: SSH/Terminal
    echo "<div style='border: 1px solid #dee2e6; margin: 10px 0;'>\n";
    echo "<h4 style='background: #e9ecef; padding: 10px; margin: 0;'>Method 2: SSH/Terminal Access</h4>\n";
    echo "<div style='padding: 15px;'>\n";
    echo "<ol>\n";
    echo "<li>SSH into your server</li>\n";
    echo "<li>Run: <code>crontab -e</code></li>\n";
    echo "<li>Paste all the cron commands from above</li>\n";
    echo "<li>Save and exit</li>\n";
    echo "<li>Verify with: <code>crontab -l</code></li>\n";
    echo "</ol>\n";
    echo "</div>\n";
    echo "</div>\n";
    
    // Method 3: Hosting Control Panel
    echo "<div style='border: 1px solid #dee2e6; margin: 10px 0;'>\n";
    echo "<h4 style='background: #e9ecef; padding: 10px; margin: 0;'>Method 3: Hosting Control Panel</h4>\n";
    echo "<div style='padding: 15px;'>\n";
    echo "<p>For shared hosting providers:</p>\n";
    echo "<ol>\n";
    echo "<li>Access your hosting control panel</li>\n";
    echo "<li>Look for 'Scheduled Tasks', 'Cron Jobs', or 'Task Scheduler'</li>\n";
    echo "<li>Add each cron job using the web interface</li>\n";
    echo "<li>Use the URLs and timing from the commands above</li>\n";
    echo "</ol>\n";
    echo "</div>\n";
    echo "</div>\n";
    
    echo "<h2>⚠️ CRITICAL ISSUES TO FIX</h2>\n";
    
    $criticalIssues = [];
    
    if (!empty($missingFiles)) {
        $criticalIssues[] = "Missing cron files: " . implode(', ', $missingFiles);
    }
    
    if (!empty($missingTables)) {
        $criticalIssues[] = "Missing database tables: " . implode(', ', $missingTables);
    }
    
    if (!defined('SITE_URL') || strpos(SITE_URL, 'localhost') !== false) {
        $criticalIssues[] = "SITE_URL not configured for production";
    }
    
    if (empty($criticalIssues)) {
        echo "<div style='background: #d4edda; padding: 20px; border: 1px solid #c3e6cb;'>\n";
        echo "<h3 style='color: #155724;'>✅ NO CRITICAL ISSUES FOUND!</h3>\n";
        echo "<p>Your cron job setup appears to be ready for production deployment.</p>\n";
        echo "</div>\n";
    } else {
        echo "<div style='background: #f8d7da; padding: 20px; border: 1px solid #f5c6cb;'>\n";
        echo "<h3 style='color: #721c24;'>❌ CRITICAL ISSUES FOUND:</h3>\n";
        foreach ($criticalIssues as $issue) {
            echo "<p>• $issue</p>\n";
        }
        echo "</div>\n";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 20px; border: 1px solid #f5c6cb;'>\n";
    echo "<h3>❌ Error During Analysis:</h3>\n";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "</div>\n";
}

echo "<h2>🛠️ AUTOMATED FIXES & OPTIMIZATIONS</h2>\n";

// Create missing tables if needed
if (!empty($missingTables)) {
    echo "<h3>🔧 Creating Missing Database Tables</h3>\n";

    $tableCreationSQL = [
        'email_queue' => "CREATE TABLE IF NOT EXISTS email_queue (
            id INT AUTO_INCREMENT PRIMARY KEY,
            recipient_email VARCHAR(255) NOT NULL,
            recipient_name VARCHAR(255),
            subject VARCHAR(500) NOT NULL,
            body TEXT NOT NULL,
            sender_email VARCHAR(255),
            sender_name VARCHAR(255),
            priority INT DEFAULT 5,
            status ENUM('pending', 'sent', 'failed', 'cancelled') DEFAULT 'pending',
            attempts INT DEFAULT 0,
            max_attempts INT DEFAULT 3,
            scheduled_at DATETIME NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            sent_at DATETIME NULL,
            error_message TEXT NULL,
            email_type VARCHAR(50) DEFAULT 'general',
            template_id INT NULL,
            member_id INT NULL,
            INDEX idx_status (status),
            INDEX idx_scheduled (scheduled_at),
            INDEX idx_created (created_at)
        )",

        'automated_emails_settings' => "CREATE TABLE IF NOT EXISTS automated_emails_settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            email_type ENUM('birthday', 'reminder', 'notification') NOT NULL,
            template_ids TEXT,
            send_time TIME DEFAULT '07:00:00',
            days_before INT DEFAULT 3,
            is_enabled TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_email_type (email_type)
        )",

        'email_schedule_logs' => "CREATE TABLE IF NOT EXISTS email_schedule_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            schedule_id INT NOT NULL,
            log_type ENUM('info', 'warning', 'error') DEFAULT 'info',
            message TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_schedule_id (schedule_id),
            INDEX idx_created_at (created_at)
        )",

        'birthday_gifts' => "CREATE TABLE IF NOT EXISTS birthday_gifts (
            id INT AUTO_INCREMENT PRIMARY KEY,
            recipient_id INT NOT NULL,
            sender_id INT NOT NULL,
            gift_type VARCHAR(100) NOT NULL,
            gift_description TEXT,
            delivery_date DATE NOT NULL,
            status ENUM('pending', 'delivered', 'failed') DEFAULT 'pending',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            delivered_at DATETIME NULL,
            INDEX idx_recipient_id (recipient_id),
            INDEX idx_delivery_date (delivery_date),
            INDEX idx_status (status)
        )"
    ];

    foreach ($missingTables as $table) {
        if (isset($tableCreationSQL[$table])) {
            try {
                $pdo->exec($tableCreationSQL[$table]);
                echo "<p style='color: green;'>✅ Created table: $table</p>\n";
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ Failed to create table $table: " . htmlspecialchars($e->getMessage()) . "</p>\n";
            }
        }
    }
}

echo "<h2>📊 MONITORING & LOGGING SETUP</h2>\n";

echo "<h3>📝 Log File Locations</h3>\n";
$logFiles = [
    'Birthday Reminders' => 'logs/birthday_reminders.log',
    'Scheduled Emails' => 'logs/scheduled_emails.log',
    'Email Queue' => 'logs/email_queue.log',
    'Event Reminders' => 'logs/event_reminders.log',
    'Birthday Gifts' => 'logs/birthday_gifts_cron.log',
    'System Cleanup' => 'logs/system_cleanup.log'
];

echo "<div style='background: #e7f3ff; padding: 15px; border: 1px solid #b3d9ff;'>\n";
foreach ($logFiles as $process => $logFile) {
    $fullPath = __DIR__ . '/' . $logFile;
    $exists = file_exists($fullPath);
    $status = $exists ? '✅ EXISTS' : '⚠️ WILL BE CREATED';
    echo "<p><strong>$process:</strong> $logFile - $status</p>\n";
}
echo "</div>\n";

echo "<h3>🔍 Monitoring Commands</h3>\n";
echo "<div style='background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6;'>\n";
echo "<h4>Check Cron Job Status:</h4>\n";
echo "<pre style='background: #343a40; color: #fff; padding: 10px;'>";
echo "# View active cron jobs\n";
echo "crontab -l\n\n";
echo "# Check cron service status\n";
echo "systemctl status cron\n\n";
echo "# View cron logs\n";
echo "tail -f /var/log/cron\n";
echo "</pre>\n";

echo "<h4>Monitor Application Logs:</h4>\n";
echo "<pre style='background: #343a40; color: #fff; padding: 10px;'>";
foreach ($logFiles as $process => $logFile) {
    echo "# Monitor $process\n";
    echo "tail -f $logFile\n\n";
}
echo "</pre>\n";
echo "</div>\n";

echo "<h2>🎯 DEPLOYMENT CHECKLIST</h2>\n";

$checklist = [
    'Upload all cron files to server' => !empty($existingFiles),
    'Configure SITE_URL for production' => defined('SITE_URL') && strpos(SITE_URL, 'localhost') === false,
    'Create required database tables' => empty($missingTables),
    'Set up cron jobs in hosting panel' => false, // Manual step
    'Test each cron job manually' => false, // Manual step
    'Monitor logs for 24 hours' => false, // Manual step
    'Verify email delivery' => false, // Manual step
    'Set up log rotation' => false // Manual step
];

echo "<div style='background: #fff3cd; padding: 20px; border: 1px solid #ffeaa7;'>\n";
foreach ($checklist as $task => $completed) {
    $status = $completed ? '✅ COMPLETE' : '⏳ PENDING';
    $color = $completed ? 'green' : 'orange';
    echo "<p style='color: $color;'>$status - $task</p>\n";
}
echo "</div>\n";

echo "<h2>🚀 QUICK START COMMANDS</h2>\n";

echo "<div style='background: #d4edda; padding: 20px; border: 1px solid #c3e6cb;'>\n";
echo "<h3>🎯 For Immediate Deployment:</h3>\n";
echo "<ol>\n";
echo "<li><strong>Update config.php:</strong> Set SITE_URL to your production domain</li>\n";
echo "<li><strong>Upload files:</strong> Ensure all cron files are uploaded to server</li>\n";
echo "<li><strong>Add to crontab:</strong> Copy the cron commands from above</li>\n";
echo "<li><strong>Test manually:</strong> Visit each cron URL with ?cron_key=fac_2024_secure_cron_8x9q2p5m</li>\n";
echo "<li><strong>Monitor logs:</strong> Check log files for successful execution</li>\n";
echo "</ol>\n";
echo "</div>\n";

echo "<div style='background: #f8d7da; padding: 20px; border: 1px solid #f5c6cb; margin: 20px 0;'>\n";
echo "<h3>⚠️ SECURITY REMINDERS:</h3>\n";
echo "<ul>\n";
echo "<li>🔐 Keep the cron key secret: fac_2024_secure_cron_8x9q2p5m</li>\n";
echo "<li>🛡️ Ensure cron files are not directly accessible via web browser</li>\n";
echo "<li>📊 Monitor logs regularly for unauthorized access attempts</li>\n";
echo "<li>🔄 Rotate logs periodically to prevent disk space issues</li>\n";
echo "</ul>\n";
echo "</div>\n";

?>
