<?php
require_once 'config.php';

echo "<h2>🔍 EXACT CORRUPTION DETECTION AND FIX</h2>\n";

global $pdo;

// Search for the exact corruption pattern from the user's message
$searchPattern = '<PERSON>" alt="<PERSON>" style="width: 150px; height: 150px; border-radius: 50%; object-fit: cover; border: 4px solid #ddd; display: block; margin: 0 auto;">';

echo "<h3>🎯 Searching for exact corruption pattern...</h3>\n";
echo "<p><strong>Pattern:</strong> <code>" . htmlspecialchars($searchPattern) . "</code></p>\n";

// Find all templates containing this exact pattern
$stmt = $pdo->prepare("SELECT id, template_name, content FROM email_templates WHERE content LIKE ?");
$stmt->execute(['%' . $searchPattern . '%']);
$corruptedTemplates = $stmt->fetchAll(PDO::FETCH_ASSOC);

if (empty($corruptedTemplates)) {
    echo "<p style='color: orange;'>⚠️ Exact pattern not found. Searching for similar patterns...</p>\n";
    
    // Search for variations
    $variations = [
        '%<PERSON>" alt=%',
        '%<PERSON>"%style=%',
        '%" alt="<PERSON>"%',
        '%border-radius: 50%; object-fit: cover%'
    ];
    
    foreach ($variations as $i => $variation) {
        $stmt = $pdo->prepare("SELECT id, template_name, content FROM email_templates WHERE content LIKE ?");
        $stmt->execute([$variation]);
        $found = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($found)) {
            echo "<h4>🔍 Found " . count($found) . " templates with variation " . ($i + 1) . ":</h4>\n";
            $corruptedTemplates = array_merge($corruptedTemplates, $found);
        }
    }
    
    // Remove duplicates
    $uniqueTemplates = [];
    foreach ($corruptedTemplates as $template) {
        $uniqueTemplates[$template['id']] = $template;
    }
    $corruptedTemplates = array_values($uniqueTemplates);
}

if (empty($corruptedTemplates)) {
    echo "<p style='color: red;'>❌ No corrupted templates found! The issue might be elsewhere.</p>\n";
    
    // Let's check all birthday templates
    echo "<h3>🔍 Checking all birthday templates...</h3>\n";
    $stmt = $pdo->prepare("SELECT id, template_name, content FROM email_templates WHERE template_name LIKE '%birthday%' OR is_birthday_template = 1");
    $stmt->execute();
    $allBirthdayTemplates = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($allBirthdayTemplates as $template) {
        echo "<h4>" . htmlspecialchars($template['template_name']) . " (ID: {$template['id']})</h4>\n";
        
        // Look for any suspicious patterns
        if (strpos($template['content'], 'Sandra Stern') !== false) {
            echo "<p style='color: red;'>🚨 Contains 'Sandra Stern'!</p>\n";
            $corruptedTemplates[] = $template;
        }
        
        if (preg_match('/[A-Za-z\s]+"[^>]*alt="[^"]*"[^>]*style="[^"]*"/', $template['content'])) {
            echo "<p style='color: red;'>🚨 Contains broken image pattern!</p>\n";
            if (!in_array($template, $corruptedTemplates)) {
                $corruptedTemplates[] = $template;
            }
        }
        
        if (strpos($template['content'], '" alt=') !== false && strpos($template['content'], '<img') === false) {
            echo "<p style='color: red;'>🚨 Has alt attribute without img tag!</p>\n";
            if (!in_array($template, $corruptedTemplates)) {
                $corruptedTemplates[] = $template;
            }
        }
    }
}

echo "<h3>🔧 FIXING CORRUPTED TEMPLATES</h3>\n";
echo "<p>Found " . count($corruptedTemplates) . " templates to fix.</p>\n";

$fixedCount = 0;

foreach ($corruptedTemplates as $template) {
    echo "<div style='border: 3px solid #dc3545; margin: 20px 0; padding: 20px;'>\n";
    echo "<h4>🔧 Fixing: " . htmlspecialchars($template['template_name']) . " (ID: {$template['id']})</h4>\n";
    
    $content = $template['content'];
    $originalContent = $content;
    $hasChanges = false;
    
    // Show the problematic area
    if (strpos($content, 'Sandra Stern') !== false) {
        $pos = strpos($content, 'Sandra Stern');
        $start = max(0, $pos - 50);
        $length = 200;
        $context = substr($content, $start, $length);
        
        echo "<h5>🔍 Problematic Content:</h5>\n";
        echo "<pre style='background: #f8d7da; padding: 10px; border: 1px solid #f5c6cb; white-space: pre-wrap;'>" . htmlspecialchars($context) . "</pre>\n";
    }
    
    // Apply multiple fix patterns
    
    // Fix 1: The exact pattern from user's message
    $exactPattern = 'Sandra Stern" alt="Sandra Stern" style="width: 150px; height: 150px; border-radius: 50%; object-fit: cover; border: 4px solid #ddd; display: block; margin: 0 auto;">';
    if (strpos($content, $exactPattern) !== false) {
        $replacement = '<img src="{birthday_member_image}" alt="{birthday_member_full_name}" style="width: 150px; height: 150px; border-radius: 50%; object-fit: cover; border: 4px solid #ddd; display: block; margin: 0 auto;">';
        $content = str_replace($exactPattern, $replacement, $content);
        $hasChanges = true;
        echo "<p style='color: green;'>✅ Fixed exact corruption pattern</p>\n";
    }
    
    // Fix 2: Any name followed by image attributes
    $pattern2 = '/([A-Za-z\s]+)"(\s*alt="[^"]*")(\s*style="[^"]*"[^>]*>)/';
    if (preg_match_all($pattern2, $content, $matches, PREG_SET_ORDER)) {
        foreach ($matches as $match) {
            $fullMatch = $match[0];
            $altAttribute = $match[2];
            $styleAndRest = $match[3];
            
            // Only fix if it doesn't already have <img
            if (strpos($fullMatch, '<img') === false) {
                $replacement = '<img src="{birthday_member_image}"' . $altAttribute . $styleAndRest;
                $content = str_replace($fullMatch, $replacement, $content);
                $hasChanges = true;
                echo "<p style='color: green;'>✅ Fixed broken image pattern: " . htmlspecialchars(substr($fullMatch, 0, 50)) . "...</p>\n";
            }
        }
    }
    
    // Fix 3: Replace any hardcoded "Sandra Stern" with placeholder
    if (strpos($content, 'Sandra Stern') !== false) {
        $content = str_replace('Sandra Stern', '{birthday_member_full_name}', $content);
        $hasChanges = true;
        echo "<p style='color: green;'>✅ Replaced hardcoded 'Sandra Stern' with placeholder</p>\n";
    }
    
    // Fix 4: Ensure we have proper img tags
    if (strpos($content, 'alt=') !== false && strpos($content, '<img') === false) {
        // Find orphaned alt attributes and wrap them in img tags
        $content = preg_replace('/(\s*alt="[^"]*"\s*style="[^"]*"[^>]*>)/', '<img src="{birthday_member_image}"$1', $content);
        $hasChanges = true;
        echo "<p style='color: green;'>✅ Added missing img tags</p>\n";
    }
    
    if ($hasChanges) {
        // Update the template
        $updateStmt = $pdo->prepare("UPDATE email_templates SET content = ? WHERE id = ?");
        $result = $updateStmt->execute([$content, $template['id']]);
        
        if ($result) {
            echo "<p style='color: green; font-weight: bold; font-size: 16px;'>✅ TEMPLATE UPDATED SUCCESSFULLY!</p>\n";
            $fixedCount++;
            
            // Show the fixed content
            if (strpos($content, '{birthday_member_image}') !== false) {
                $pos = strpos($content, '{birthday_member_image}');
                $start = max(0, $pos - 50);
                $length = 200;
                $fixedContext = substr($content, $start, $length);
                
                echo "<h5>✅ Fixed Content:</h5>\n";
                echo "<pre style='background: #d4edda; padding: 10px; border: 1px solid #c3e6cb; white-space: pre-wrap;'>" . htmlspecialchars($fixedContext) . "</pre>\n";
            }
        } else {
            echo "<p style='color: red; font-weight: bold;'>❌ FAILED TO UPDATE TEMPLATE!</p>\n";
        }
    } else {
        echo "<p style='color: blue;'>ℹ️ No changes needed for this template.</p>\n";
    }
    
    echo "</div>\n";
}

echo "<div style='border: 3px solid #28a745; margin: 20px 0; padding: 20px; background: #f8fff9;'>\n";
echo "<h3>🎉 FIX SUMMARY</h3>\n";
echo "<p><strong>Templates processed:</strong> " . count($corruptedTemplates) . "</p>\n";
echo "<p><strong>Templates fixed:</strong> $fixedCount</p>\n";

if ($fixedCount > 0) {
    echo "<h4>✅ What was fixed:</h4>\n";
    echo "<ul>\n";
    echo "<li>Removed hardcoded 'Sandra Stern' text</li>\n";
    echo "<li>Added proper &lt;img src='{birthday_member_image}' tags</li>\n";
    echo "<li>Replaced hardcoded names with {birthday_member_full_name} placeholder</li>\n";
    echo "<li>Fixed broken image attribute patterns</li>\n";
    echo "</ul>\n";
    
    echo "<h4>🧪 Test Now:</h4>\n";
    echo "<p>Try sending a birthday notification again. You should now see:</p>\n";
    echo "<ul>\n";
    echo "<li>✅ Proper member image instead of broken text</li>\n";
    echo "<li>✅ Correct member name in alt text</li>\n";
    echo "<li>✅ No more 'Sandra Stern' corruption</li>\n";
    echo "</ul>\n";
} else {
    echo "<p style='color: orange;'>⚠️ No templates were fixed. The corruption might be in a different location.</p>\n";
    echo "<p>Please check:</p>\n";
    echo "<ul>\n";
    echo "<li>Template preview system</li>\n";
    echo "<li>Placeholder replacement function</li>\n";
    echo "<li>Email sending process</li>\n";
    echo "</ul>\n";
}
echo "</div>\n";

?>
