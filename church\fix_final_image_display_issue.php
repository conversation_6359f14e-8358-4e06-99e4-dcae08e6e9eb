<?php
require_once 'config.php';

echo "<h2>🔧 FINAL FIX: Image Display Issue</h2>\n";

global $pdo;

// The issue is that we still have "<Sandra Stern" appearing instead of an image
// This suggests the template has a broken pattern like: <{member_image} or similar

echo "<h3>🔍 Step 1: Find the Exact Broken Pattern</h3>\n";

// Get the template that's being previewed (ID 46 from the URL)
$templateId = 46;
$stmt = $pdo->prepare("SELECT * FROM email_templates WHERE id = ?");
$stmt->execute([$templateId]);
$template = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$template) {
    echo "<p style='color: red;'>❌ Template ID $templateId not found!</p>\n";
    exit;
}

echo "<p><strong>Template:</strong> " . htmlspecialchars($template['template_name']) . "</p>\n";

$content = $template['content'];

// Look for the specific broken pattern that would cause "<<PERSON>" to appear
echo "<h4>🔍 Searching for Broken Patterns:</h4>\n";

// Pattern 1: <{placeholder} (missing closing bracket or malformed)
if (preg_match_all('/<\{[^}]*\}?/', $content, $matches)) {
    echo "<div style='background: #f8d7da; padding: 10px; border: 1px solid #f5c6cb;'>\n";
    echo "<h5>❌ Found malformed placeholder patterns:</h5>\n";
    foreach ($matches[0] as $match) {
        echo "<p><code>" . htmlspecialchars($match) . "</code></p>\n";
    }
    echo "</div>\n";
}

// Pattern 2: Look for any pattern that starts with < followed by text
if (preg_match_all('/<[A-Za-z\s][^>]*/', $content, $matches)) {
    echo "<div style='background: #fff3cd; padding: 10px; border: 1px solid #ffeaa7;'>\n";
    echo "<h5>⚠️ Found patterns starting with &lt; followed by text:</h5>\n";
    foreach ($matches[0] as $match) {
        if (!preg_match('/^<(img|div|p|span|h[1-6]|a|br|hr)/i', $match)) {
            echo "<p><code>" . htmlspecialchars($match) . "</code></p>\n";
        }
    }
    echo "</div>\n";
}

// Pattern 3: Look for incomplete img tags
if (preg_match_all('/<img[^>]*(?!>)/', $content, $matches)) {
    echo "<div style='background: #f8d7da; padding: 10px; border: 1px solid #f5c6cb;'>\n";
    echo "<h5>❌ Found incomplete img tags:</h5>\n";
    foreach ($matches[0] as $match) {
        echo "<p><code>" . htmlspecialchars($match) . "</code></p>\n";
    }
    echo "</div>\n";
}

// Show the raw content around where the image should be
echo "<h4>📄 Raw Template Content Analysis:</h4>\n";

// Look for the area around where Sandra Stern appears
if (strpos($content, 'Sandra') !== false) {
    $pos = strpos($content, 'Sandra');
    $start = max(0, $pos - 200);
    $length = 500;
    $context = substr($content, $start, $length);
    
    echo "<div style='background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6;'>\n";
    echo "<h5>Context around 'Sandra':</h5>\n";
    echo "<pre style='white-space: pre-wrap; font-size: 12px;'>" . htmlspecialchars($context) . "</pre>\n";
    echo "</div>\n";
}

// Look for member_image placeholder usage
if (strpos($content, 'member_image') !== false) {
    echo "<h4>🖼️ Member Image Placeholder Usage:</h4>\n";
    
    // Find all occurrences
    $pos = 0;
    $occurrences = [];
    while (($pos = strpos($content, 'member_image', $pos)) !== false) {
        $start = max(0, $pos - 100);
        $length = 250;
        $context = substr($content, $start, $length);
        $occurrences[] = $context;
        $pos += strlen('member_image');
    }
    
    foreach ($occurrences as $i => $context) {
        echo "<div style='background: #e7f3ff; padding: 10px; margin: 10px 0; border-left: 4px solid #007bff;'>\n";
        echo "<h5>Occurrence " . ($i + 1) . ":</h5>\n";
        echo "<pre style='white-space: pre-wrap; font-size: 12px;'>" . htmlspecialchars($context) . "</pre>\n";
        echo "</div>\n";
    }
}

echo "<h3>🔧 Step 2: Apply Targeted Fix</h3>\n";

$originalContent = $content;
$hasChanges = false;

// Fix 1: Remove any malformed < patterns before placeholders
$content = preg_replace('/<(\{[^}]+\})/', '$1', $content);
if ($content !== $originalContent) {
    echo "<p style='color: green;'>✅ Fixed malformed &lt;{placeholder} patterns</p>\n";
    $hasChanges = true;
    $originalContent = $content;
}

// Fix 2: Fix incomplete img tags
$content = preg_replace('/<img([^>]*)(?<!>)$/', '<img$1>', $content);
if ($content !== $originalContent) {
    echo "<p style='color: green;'>✅ Fixed incomplete img tags</p>\n";
    $hasChanges = true;
    $originalContent = $content;
}

// Fix 3: Look for and fix the specific pattern causing "<Sandra Stern"
// This might be something like: <{birthday_member_full_name}
$content = preg_replace('/<\{birthday_member_full_name\}/', '{birthday_member_full_name}', $content);
if ($content !== $originalContent) {
    echo "<p style='color: green;'>✅ Fixed &lt;{birthday_member_full_name} pattern</p>\n";
    $hasChanges = true;
    $originalContent = $content;
}

// Fix 4: Look for any other malformed patterns
$content = preg_replace('/<\{([^}]+)\}/', '{$1}', $content);
if ($content !== $originalContent) {
    echo "<p style='color: green;'>✅ Fixed other malformed placeholder patterns</p>\n";
    $hasChanges = true;
    $originalContent = $content;
}

// Fix 5: Ensure proper image placeholder structure
// Look for patterns like: {member_image} followed by broken attributes
$content = preg_replace('/\{member_image\}([^<]*alt="[^"]*"[^<]*style="[^"]*"[^>]*>)/', '<img src="{birthday_member_image_url}"$1', $content);
if ($content !== $originalContent) {
    echo "<p style='color: green;'>✅ Fixed broken image structure after {member_image}</p>\n";
    $hasChanges = true;
    $originalContent = $content;
}

// Fix 6: Look for any remaining broken image patterns
$content = preg_replace('/([A-Za-z\s]+)"(\s*alt="[^"]*")(\s*style="[^"]*"[^>]*>)/', '<img src="{birthday_member_image_url}"$2$3', $content);
if ($content !== $originalContent) {
    echo "<p style='color: green;'>✅ Fixed remaining broken image patterns</p>\n";
    $hasChanges = true;
}

if ($hasChanges) {
    // Update the template
    $updateStmt = $pdo->prepare("UPDATE email_templates SET content = ? WHERE id = ?");
    $result = $updateStmt->execute([$content, $templateId]);
    
    if ($result) {
        echo "<div style='border: 3px solid #28a745; margin: 20px 0; padding: 20px; background: #f8fff9;'>\n";
        echo "<h3>✅ TEMPLATE UPDATED SUCCESSFULLY!</h3>\n";
        echo "<p>The template has been fixed. The image should now display correctly.</p>\n";
        
        echo "<h4>📋 Changes Made:</h4>\n";
        echo "<div style='display: grid; grid-template-columns: 1fr 1fr; gap: 20px;'>\n";
        echo "<div>\n";
        echo "<h5>❌ Before (Broken):</h5>\n";
        echo "<pre style='background: #f8d7da; padding: 10px; font-size: 11px; max-height: 200px; overflow-y: auto;'>" . htmlspecialchars(substr($template['content'], 0, 800)) . "</pre>\n";
        echo "</div>\n";
        echo "<div>\n";
        echo "<h5>✅ After (Fixed):</h5>\n";
        echo "<pre style='background: #d4edda; padding: 10px; font-size: 11px; max-height: 200px; overflow-y: auto;'>" . htmlspecialchars(substr($content, 0, 800)) . "</pre>\n";
        echo "</div>\n";
        echo "</div>\n";
        
        echo "<h4>🧪 Next Steps:</h4>\n";
        echo "<ol>\n";
        echo "<li><strong>Refresh the template preview</strong> - The image should now appear</li>\n";
        echo "<li><strong>Test email sending</strong> - Verify the image embeds correctly</li>\n";
        echo "<li><strong>Check other templates</strong> - Apply similar fixes if needed</li>\n";
        echo "</ol>\n";
        
        echo "</div>\n";
    } else {
        echo "<p style='color: red; font-weight: bold;'>❌ Failed to update template!</p>\n";
    }
} else {
    echo "<p style='color: blue;'>ℹ️ No obvious fixes found. The issue might be in the preview system or placeholder replacement logic.</p>\n";
    
    echo "<h4>🔍 Additional Debugging Needed:</h4>\n";
    echo "<ul>\n";
    echo "<li>Check the preview_template.php file for issues</li>\n";
    echo "<li>Verify the placeholder replacement function</li>\n";
    echo "<li>Check if the image file exists and is accessible</li>\n";
    echo "</ul>\n";
}

echo "<div style='background: #e7f3ff; padding: 15px; border: 1px solid #b3d9ff; margin: 20px 0;'>\n";
echo "<h4>🎯 Expected Result:</h4>\n";
echo "<p>After this fix, instead of seeing:</p>\n";
echo "<code>&lt;Sandra Stern</code>\n";
echo "<p>You should see:</p>\n";
echo "<p>✅ A proper image of Sandra Stern with correct alt text</p>\n";
echo "</div>\n";

?>
