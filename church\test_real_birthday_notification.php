<?php
/**
 * Test script to simulate the exact birthday notification process
 */

require_once 'config.php';
require_once 'send_birthday_reminders.php';

echo "<h2>Real Birthday Notification Test</h2>\n";

try {
    // Create a BirthdayReminder instance
    $reminder = new BirthdayReminder($pdo);
    
    // Get <PERSON> (the birthday member from the logs)
    $stmt = $pdo->prepare("SELECT * FROM members WHERE full_name LIKE '%Jennifer%' AND image_path IS NOT NULL LIMIT 1");
    $stmt->execute();
    $birthdayMember = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$birthdayMember) {
        echo "❌ Jennifer not found\n";
        exit;
    }
    
    // Get a recipient (Sandra from the logs)
    $stmt = $pdo->prepare("SELECT * FROM members WHERE full_name LIKE '%Sandra%' LIMIT 1");
    $stmt->execute();
    $recipient = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$recipient) {
        // Get any other member as recipient
        $stmt = $pdo->prepare("SELECT * FROM members WHERE id != ? AND status = 'active' LIMIT 1");
        $stmt->execute([$birthdayMember['id']]);
        $recipient = $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    if (!$recipient) {
        echo "❌ No recipient found\n";
        exit;
    }
    
    echo "<h3>Test Setup:</h3>\n";
    echo "<p><strong>Birthday Member:</strong> {$birthdayMember['full_name']}</p>\n";
    echo "<p><strong>Birthday Member Image:</strong> {$birthdayMember['image_path']}</p>\n";
    echo "<p><strong>Recipient:</strong> {$recipient['full_name']}</p>\n";
    echo "<p><strong>Recipient Image:</strong> " . ($recipient['image_path'] ?: 'None') . "</p>\n";
    
    // Get a notification template
    $notificationTemplate = getRandomTemplateForType('notification');
    
    if (!$notificationTemplate) {
        echo "❌ No notification template found\n";
        exit;
    }
    
    echo "<p><strong>Template:</strong> {$notificationTemplate['template_name']}</p>\n";
    
    // Test the exact same process as the real system
    echo "<h3>Calling sendMemberBirthdayNotifications (same as real system)...</h3>\n";

    // Enable detailed logging
    error_log("=== BIRTHDAY NOTIFICATION TEST START ===");

    // Call the public method that handles birthday notifications
    $result = $reminder->sendMemberBirthdayNotifications(
        $birthdayMember['id'], // birthdayMemberId
        $notificationTemplate['id'], // templateId
        0                      // daysUntilBirthday (0 for today)
    );

    error_log("=== BIRTHDAY NOTIFICATION TEST END ===");

    $success = $result && $result['total_sent'] > 0;
    
    if ($success) {
        echo "<p style='color: green;'>✅ Birthday notification sent successfully!</p>\n";
        
        // Check the latest logs
        echo "<h3>Latest Birthday Image Embedding Log:</h3>\n";
        $logContent = file_get_contents('logs/birthday_image_embedding.log');
        $logLines = explode("\n", $logContent);
        
        // Get the last few entries
        $latestEntries = array_slice($logLines, -20);
        $latestEntries = array_filter($latestEntries, function($line) {
            return !empty(trim($line));
        });
        
        echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd; max-height: 300px; overflow-y: auto;'>";
        echo htmlspecialchars(implode("\n", $latestEntries));
        echo "</pre>\n";
        
        // Check if URL replacement worked
        $lastEntry = end($latestEntries);
        if (strpos($lastEntry, 'URL replaced in email body: YES') !== false) {
            echo "<p style='color: green;'>✅ URL replacement worked!</p>\n";
        } elseif (strpos($lastEntry, 'URL replaced in email body: NO') !== false) {
            echo "<p style='color: red;'>❌ URL replacement failed</p>\n";
        }
        
    } else {
        echo "<p style='color: red;'>❌ Failed to send birthday notification</p>\n";
        
        // Check for errors
        global $last_email_error;
        if ($last_email_error) {
            echo "<p style='color: red;'>Error: " . htmlspecialchars($last_email_error) . "</p>\n";
        }
    }
    
    echo "<h3>Email Debug Log (last 20 lines):</h3>\n";
    $debugLog = file_get_contents('logs/email_debug.log');
    $debugLines = explode("\n", $debugLog);
    $latestDebugEntries = array_slice($debugLines, -20);
    $latestDebugEntries = array_filter($latestDebugEntries, function($line) {
        return !empty(trim($line));
    });
    
    echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd; max-height: 300px; overflow-y: auto;'>";
    echo htmlspecialchars(implode("\n", $latestDebugEntries));
    echo "</pre>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "<p>Stack trace:</p>\n";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>\n";
}
?>
