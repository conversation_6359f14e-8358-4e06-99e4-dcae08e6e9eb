<?php
/**
 * Fix the remaining birthday notification templates (1 and 2)
 */

require_once 'config.php';

echo "<h2>Fixing Remaining Birthday Notification Templates</h2>\n";

try {
    // Fix Template 1 (ID: 37) - has member-section class
    echo "<h3>Fixing Template 1 (ID: 37)</h3>\n";
    $stmt = $pdo->prepare('SELECT content FROM email_templates WHERE id = 37');
    $stmt->execute();
    $template1 = $stmt->fetch();
    
    if ($template1) {
        $content = $template1['content'];
        $originalContent = $content;
        
        // Replace member-section div containing {member_image}
        $pattern = '/<div[^>]*class=["\']member-section["\'][^>]*>\s*\{member_image\}\s*<\/div>/i';
        $replacement = '<div class="member-section"><img src="{member_image}" alt="{birthday_member_full_name}" style="width: 150px; height: 150px; border-radius: 50%; object-fit: cover; border: 4px solid #ddd; display: block; margin: 0 auto;"></div>';
        
        $content = preg_replace($pattern, $replacement, $content);
        
        if ($content !== $originalContent) {
            $updateStmt = $pdo->prepare('UPDATE email_templates SET content = ? WHERE id = 37');
            $result = $updateStmt->execute([$content]);
            
            if ($result) {
                echo "<p style='color: green;'>✅ Template 1 updated successfully</p>\n";
            } else {
                echo "<p style='color: red;'>❌ Failed to update Template 1</p>\n";
            }
        } else {
            echo "<p>ℹ️ No changes made to Template 1</p>\n";
        }
    }
    
    // Fix Template 2 (ID: 46) - has member-photo class
    echo "<h3>Fixing Template 2 (ID: 46)</h3>\n";
    $stmt = $pdo->prepare('SELECT content FROM email_templates WHERE id = 46');
    $stmt->execute();
    $template2 = $stmt->fetch();
    
    if ($template2) {
        $content = $template2['content'];
        $originalContent = $content;
        
        // Replace member-photo div containing {member_image}
        $pattern = '/<div[^>]*class=["\']member-photo["\'][^>]*>\s*\{member_image\}\s*<\/div>/i';
        $replacement = '<div class="member-photo"><img src="{member_image}" alt="{birthday_member_full_name}" style="width: 150px; height: 150px; border-radius: 50%; object-fit: cover; border: 4px solid #ddd; display: block; margin: 0 auto;"></div>';
        
        $content = preg_replace($pattern, $replacement, $content);
        
        if ($content !== $originalContent) {
            $updateStmt = $pdo->prepare('UPDATE email_templates SET content = ? WHERE id = 46');
            $result = $updateStmt->execute([$content]);
            
            if ($result) {
                echo "<p style='color: green;'>✅ Template 2 updated successfully</p>\n";
            } else {
                echo "<p style='color: red;'>❌ Failed to update Template 2</p>\n";
            }
        } else {
            echo "<p>ℹ️ No changes made to Template 2</p>\n";
        }
    }
    
    echo "<h3>Verification</h3>\n";
    echo "<p>Let's verify all templates now have proper img tags:</p>\n";
    
    $stmt = $pdo->prepare('SELECT id, template_name, content FROM email_templates WHERE template_name LIKE "%notification%"');
    $stmt->execute();
    $templates = $stmt->fetchAll();
    
    foreach ($templates as $template) {
        echo "<h4>Template: {$template['template_name']} (ID: {$template['id']})</h4>\n";
        
        // Check if it has img tags now
        if (preg_match('/<img[^>]+src=["\']?\{member_image\}["\']?[^>]*>/i', $template['content'])) {
            echo "<p style='color: green;'>✅ Has proper img tag with {member_image} src</p>\n";
        } else {
            echo "<p style='color: red;'>❌ Still missing proper img tag</p>\n";
            
            // Show current state
            if (strpos($template['content'], '{member_image}') !== false) {
                $pos = strpos($template['content'], '{member_image}');
                $start = max(0, $pos - 100);
                $length = 200;
                $context = substr($template['content'], $start, $length);
                echo "<p><strong>Current context:</strong></p>\n";
                echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd;'>";
                echo htmlspecialchars($context);
                echo "</pre>\n";
            }
        }
    }
    
    echo "<h3>Summary</h3>\n";
    echo "<p>All birthday notification templates should now have proper img tags for displaying member images.</p>\n";
    echo "<p><strong>What was fixed:</strong></p>\n";
    echo "<ul>\n";
    echo "<li>Template 1: Replaced member-section div with img tag</li>\n";
    echo "<li>Template 2: Replaced member-photo div with img tag</li>\n";
    echo "<li>Template 3: Already fixed in previous step</li>\n";
    echo "</ul>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}
?>
