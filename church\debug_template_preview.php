<?php
require_once 'config.php';

echo "<h2>🔍 DEBUG: Template Preview System</h2>\n";

global $pdo;

// Get the specific template that's causing issues
$templateName = 'Member Upcoming Birthday Notification 2';
$stmt = $pdo->prepare("SELECT * FROM email_templates WHERE template_name = ?");
$stmt->execute([$templateName]);
$template = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$template) {
    echo "<p style='color: red;'>❌ Template '$templateName' not found!</p>\n";
    exit;
}

echo "<h3>📋 Template: " . htmlspecialchars($template['template_name']) . "</h3>\n";

// Show raw template content
echo "<h4>📄 Raw Template Content:</h4>\n";
echo "<textarea style='width: 100%; height: 200px; font-family: monospace; font-size: 12px;'>" . htmlspecialchars($template['content']) . "</textarea>\n";

// Check for corruption in raw content
if (strpos($template['content'], '<PERSON>') !== false) {
    echo "<p style='color: red; font-weight: bold;'>🚨 RAW TEMPLATE CONTAINS 'Sandra Stern'!</p>\n";
} else {
    echo "<p style='color: green;'>✅ Raw template does not contain 'Sandra Stern'</p>\n";
}

// Now simulate the preview process
echo "<h3>🔄 Simulating Preview Process</h3>\n";

// Step 1: Get sample member data (like the preview system does)
$stmt = $pdo->prepare("SELECT * FROM members WHERE image_path IS NOT NULL AND image_path != '' LIMIT 1");
$stmt->execute();
$sample_member = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$sample_member) {
    $sample_member = [
        'id' => 1,
        'full_name' => 'John Doe',
        'first_name' => 'John',
        'last_name' => 'Doe',
        'email' => '<EMAIL>',
        'image_path' => 'assets/img/default-avatar.png'
    ];
}

echo "<p><strong>Sample Member:</strong> " . htmlspecialchars($sample_member['full_name']) . "</p>\n";

// Step 2: Create birthday member data (like preview_template.php does)
$birthday_member = [
    'full_name' => 'Jane Smith',
    'email' => '<EMAIL>',
    'phone_number' => '(*************',
    'birth_date' => date('Y-m-d', strtotime('1990-' . date('m-d', strtotime('+3 days')))),
    'image_path' => 'assets/img/default-avatar.png'
];

// Override with real member if available
$stmt = $pdo->prepare("SELECT * FROM members WHERE id != ? AND image_path IS NOT NULL AND image_path != '' ORDER BY id LIMIT 1");
$stmt->execute([$sample_member['id']]);
if ($row = $stmt->fetch()) {
    $birthday_member = array_merge($birthday_member, $row);
}

echo "<p><strong>Birthday Member:</strong> " . htmlspecialchars($birthday_member['full_name']) . "</p>\n";

// Step 3: Create image HTML (like preview system does)
$birthdayMemberImagePath = '../' . ltrim($birthday_member['image_path'], '/');
$birthdayMemberImageHtml = '<img src="' . $birthdayMemberImagePath . '" alt="' .
    htmlspecialchars($birthday_member['full_name']) .
    '" style="width: 140px; height: 140px; border-radius: 50%; object-fit: cover; border: 5px solid #ff758c; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">';

echo "<h4>🖼️ Generated Image HTML:</h4>\n";
echo "<code style='background: #f8f9fa; padding: 10px; display: block;'>" . htmlspecialchars($birthdayMemberImageHtml) . "</code>\n";

// Step 4: Create replacement array
$birthday_member_replacements = [
    '{birthday_member_name}' => explode(' ', $birthday_member['full_name'])[0],
    '{birthday_member_full_name}' => $birthday_member['full_name'],
    '{birthday_member_email}' => $birthday_member['email'] ?? '',
    '{birthday_member_phone}' => $birthday_member['phone_number'] ?? 'Not provided',
    '{birthday_member_image}' => $birthdayMemberImageHtml,
    '{birthday_member_photo_url}' => $birthdayMemberImagePath,
    '{member_image}' => $birthdayMemberImageHtml,
    '{member_image_url}' => $birthdayMemberImagePath,
    '{image_path}' => $birthdayMemberImagePath,
    '{profile_photo}' => $birthdayMemberImageHtml,
    '{days_text}' => 'in 3 days',
    '{upcoming_birthday_formatted}' => date('l, F j, Y', strtotime('+3 days'))
];

echo "<h4>📝 Replacement Values:</h4>\n";
foreach ($birthday_member_replacements as $placeholder => $value) {
    $displayValue = strlen($value) > 100 ? substr($value, 0, 100) . '...' : $value;
    echo "<p><code>$placeholder</code> → " . htmlspecialchars($displayValue) . "</p>\n";
}

// Step 5: Apply replacements (like preview system does)
$content = $template['content'];

echo "<h4>🔄 Step-by-Step Replacement Process:</h4>\n";

// First apply birthday member replacements
$contentBefore = $content;
$content = str_replace(array_keys($birthday_member_replacements), array_values($birthday_member_replacements), $content);

echo "<h5>After Birthday Member Replacements:</h5>\n";
if ($content !== $contentBefore) {
    echo "<p style='color: green;'>✅ Changes made during birthday member replacement</p>\n";
    
    // Show differences
    $changes = [];
    foreach ($birthday_member_replacements as $placeholder => $value) {
        if (strpos($contentBefore, $placeholder) !== false) {
            $changes[] = "$placeholder → " . (strlen($value) > 50 ? substr($value, 0, 50) . '...' : $value);
        }
    }
    
    if (!empty($changes)) {
        echo "<p><strong>Replacements made:</strong></p>\n";
        foreach ($changes as $change) {
            echo "<p style='margin-left: 20px;'>• " . htmlspecialchars($change) . "</p>\n";
        }
    }
} else {
    echo "<p style='color: orange;'>⚠️ No changes made during birthday member replacement</p>\n";
}

// Check for Sandra Stern after replacement
if (strpos($content, 'Sandra Stern') !== false) {
    echo "<p style='color: red; font-weight: bold;'>🚨 'Sandra Stern' STILL PRESENT after replacement!</p>\n";
    
    // Find where it appears
    $pos = strpos($content, 'Sandra Stern');
    $start = max(0, $pos - 100);
    $length = 300;
    $context = substr($content, $start, $length);
    
    echo "<h5>Context where 'Sandra Stern' appears:</h5>\n";
    echo "<pre style='background: #f8d7da; padding: 10px; border: 1px solid #f5c6cb; white-space: pre-wrap;'>" . htmlspecialchars($context) . "</pre>\n";
} else {
    echo "<p style='color: green;'>✅ No 'Sandra Stern' found after replacement</p>\n";
}

// Step 6: Apply regular member placeholders (like preview system does)
$contentBefore2 = $content;
$content = replaceTemplatePlaceholders($content, $sample_member, true); // Skip member image processing

echo "<h5>After Regular Member Replacements:</h5>\n";
if ($content !== $contentBefore2) {
    echo "<p style='color: green;'>✅ Changes made during regular member replacement</p>\n";
} else {
    echo "<p style='color: blue;'>ℹ️ No changes made during regular member replacement</p>\n";
}

// Final check
if (strpos($content, 'Sandra Stern') !== false) {
    echo "<p style='color: red; font-weight: bold; font-size: 16px;'>🚨 FINAL RESULT: 'Sandra Stern' STILL PRESENT!</p>\n";
} else {
    echo "<p style='color: green; font-weight: bold; font-size: 16px;'>✅ FINAL RESULT: No 'Sandra Stern' found!</p>\n";
}

// Show final processed content
echo "<h4>📄 Final Processed Content:</h4>\n";
echo "<div style='border: 1px solid #ddd; padding: 15px; background: white; max-height: 400px; overflow-y: auto;'>\n";
echo $content;
echo "</div>\n";

echo "<h3>🎯 Analysis Summary</h3>\n";
echo "<p>This debug shows exactly what happens during the template preview process.</p>\n";
echo "<p>If 'Sandra Stern' appears in the final result, it means:</p>\n";
echo "<ol>\n";
echo "<li>The raw template contains hardcoded 'Sandra Stern' text</li>\n";
echo "<li>The placeholder replacement is not working correctly</li>\n";
echo "<li>There's a bug in the preview system</li>\n";
echo "</ol>\n";

?>
