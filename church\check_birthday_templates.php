<?php
require_once 'config.php';

echo "<h2>Birthday Template Analysis</h2>\n";

global $pdo;
$stmt = $pdo->prepare('SELECT id, template_name, content FROM email_templates WHERE is_birthday_template = 1 ORDER BY id');
$stmt->execute();
$templates = $stmt->fetchAll(PDO::FETCH_ASSOC);

foreach ($templates as $template) {
    echo "<div style='border: 2px solid #007bff; margin: 20px 0; padding: 15px;'>\n";
    echo "<h3>Template ID: {$template['id']} - " . htmlspecialchars($template['template_name']) . "</h3>\n";
    
    // Check for image placeholders
    $content = $template['content'];
    $hasImagePlaceholder = false;
    $imageIssues = [];
    
    // Check for various image placeholders
    if (strpos($content, '{member_image}') !== false) {
        echo "<p style='color: green;'>✅ Contains {member_image} placeholder</p>\n";
        $hasImagePlaceholder = true;
    }
    
    if (strpos($content, '{birthday_member_image}') !== false) {
        echo "<p style='color: green;'>✅ Contains {birthday_member_image} placeholder</p>\n";
        $hasImagePlaceholder = true;
    }
    
    if (strpos($content, '{birthday_member_photo_url}') !== false) {
        echo "<p style='color: green;'>✅ Contains {birthday_member_photo_url} placeholder</p>\n";
        $hasImagePlaceholder = true;
    }
    
    // Check for problematic patterns
    if (preg_match('/src="[^{][^"]*"/', $content)) {
        echo "<p style='color: red;'>❌ Contains hardcoded image src attributes</p>\n";
        $imageIssues[] = "Hardcoded image sources";
    }
    
    if (preg_match('/src="{[^}]*name[^}]*}"/', $content)) {
        echo "<p style='color: red;'>❌ Image src uses name placeholder instead of image placeholder</p>\n";
        $imageIssues[] = "Name placeholder in image src";
    }
    
    if (!$hasImagePlaceholder) {
        echo "<p style='color: orange;'>⚠️ No image placeholders found</p>\n";
    }
    
    // Show a snippet of the content around image tags
    if (preg_match_all('/<img[^>]*>/i', $content, $matches)) {
        echo "<h4>Image Tags Found:</h4>\n";
        foreach ($matches[0] as $imgTag) {
            echo "<p><code>" . htmlspecialchars($imgTag) . "</code></p>\n";
        }
    }
    
    echo "</div>\n";
}

if (empty($templates)) {
    echo "<p style='color: red;'>❌ No birthday templates found in database!</p>\n";
}

?>
