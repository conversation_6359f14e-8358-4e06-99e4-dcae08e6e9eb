<?php
require_once 'config.php';

echo "<h2>🔧 CRITICAL FIX: Nested IMG Tags & Wrong Placeholders</h2>\n";

global $pdo;

// Get template ID 46
$templateId = 46;
$stmt = $pdo->prepare("SELECT * FROM email_templates WHERE id = ?");
$stmt->execute([$templateId]);
$template = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$template) {
    echo "<p style='color: red;'>❌ Template ID $templateId not found!</p>\n";
    exit;
}

echo "<h3>📋 Template: " . htmlspecialchars($template['template_name']) . "</h3>\n";

$content = $template['content'];

echo "<h4>🔍 Searching for Nested IMG Tags:</h4>\n";

// Look for the specific nested img pattern
if (preg_match_all('/<img\s+src="<img\s+src="[^"]*"[^>]*>/', $content, $matches)) {
    echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb;'>\n";
    echo "<h5>❌ Found " . count($matches[0]) . " nested IMG tags:</h5>\n";
    foreach ($matches[0] as $i => $match) {
        echo "<p><strong>Nested IMG " . ($i + 1) . ":</strong></p>\n";
        echo "<code style='background: #fff; padding: 5px; display: block; margin: 5px 0;'>" . htmlspecialchars($match) . "</code>\n";
    }
    echo "</div>\n";
}

// Look for wrong placeholders
echo "<h4>🔍 Checking Placeholder Usage:</h4>\n";

$wrongPlaceholders = [
    '{birthday_member_image_url}' => 'Should be {birthday_member_photo_url} or {member_image}',
    '{member_image_url}' => 'Should be {birthday_member_photo_url} or {member_image}'
];

foreach ($wrongPlaceholders as $placeholder => $correction) {
    if (strpos($content, $placeholder) !== false) {
        echo "<p style='color: orange;'>⚠️ Found wrong placeholder: <code>$placeholder</code> - $correction</p>\n";
    }
}

echo "<h3>🔧 APPLYING COMPREHENSIVE FIX</h3>\n";

$originalContent = $content;
$fixedContent = $content;

// Fix 1: Remove nested img tags - replace with simple placeholder
$fixedContent = preg_replace('/<img\s+src="<img\s+src="[^"]*"[^>]*>/', '{member_image}', $fixedContent);

// Fix 2: Remove any remaining nested patterns
$fixedContent = preg_replace('/<img\s+src="<img[^>]*>/', '{member_image}', $fixedContent);

// Fix 3: Fix wrong placeholders
$fixedContent = str_replace('{birthday_member_image_url}', '{birthday_member_photo_url}', $fixedContent);
$fixedContent = str_replace('{member_image_url}', '{birthday_member_photo_url}', $fixedContent);

// Fix 4: Clean up any remaining malformed img tags
$fixedContent = preg_replace('/<img\s+src="[^"]*<img[^>]*>/', '{member_image}', $fixedContent);

// Fix 5: Remove any stray &lt; entities
$fixedContent = str_replace('&lt;', '', $fixedContent);

// Fix 6: Ensure proper image structure - look for broken patterns and replace with clean structure
$patterns = [
    // Pattern: broken img with multiple src attributes
    '/<img[^>]*src="[^"]*"[^>]*src="[^"]*"[^>]*>/' => '{member_image}',
    
    // Pattern: img tag with placeholder that should be standalone
    '/<img[^>]*\{member_image\}[^>]*>/' => '{member_image}',
    
    // Pattern: any remaining malformed img tags
    '/<img[^>]*<[^>]*>/' => '{member_image}'
];

foreach ($patterns as $pattern => $replacement) {
    $fixedContent = preg_replace($pattern, $replacement, $fixedContent);
}

if ($fixedContent !== $originalContent) {
    echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb;'>\n";
    echo "<h4>✅ Issues Found and Fixed!</h4>\n";
    
    // Show the specific changes
    if (strpos($originalContent, 'src="<img') !== false) {
        echo "<p>• ✅ Removed nested &lt;img src=\"&lt;img src=\" patterns</p>\n";
    }
    if (strpos($originalContent, '{birthday_member_image_url}') !== false) {
        echo "<p>• ✅ Fixed wrong placeholder {birthday_member_image_url} → {birthday_member_photo_url}</p>\n";
    }
    if (strpos($originalContent, '&lt;') !== false) {
        echo "<p>• ✅ Removed stray &amp;lt; entities</p>\n";
    }
    echo "</div>\n";
    
    // Update the database
    $updateStmt = $pdo->prepare("UPDATE email_templates SET content = ? WHERE id = ?");
    $result = $updateStmt->execute([$fixedContent, $templateId]);
    
    if ($result) {
        echo "<div style='border: 3px solid #28a745; margin: 20px 0; padding: 20px; background: #f8fff9;'>\n";
        echo "<h3>🎉 TEMPLATE COMPLETELY FIXED!</h3>\n";
        echo "<p><strong>Template ID $templateId has been updated with proper image handling.</strong></p>\n";
        
        // Show before/after for the image section
        echo "<h4>📋 Image Section Before/After:</h4>\n";
        
        // Find the image section in both versions
        $beforeImageSection = '';
        $afterImageSection = '';
        
        if (preg_match('/.*member.*image.*alt.*style.*/i', $originalContent, $beforeMatch)) {
            $beforeImageSection = $beforeMatch[0];
        }
        if (preg_match('/.*member.*image.*alt.*style.*/i', $fixedContent, $afterMatch)) {
            $afterImageSection = $afterMatch[0];
        }
        
        echo "<div style='display: grid; grid-template-columns: 1fr 1fr; gap: 20px;'>\n";
        echo "<div>\n";
        echo "<h5>❌ Before (Broken):</h5>\n";
        echo "<pre style='background: #f8d7da; padding: 10px; font-size: 11px; white-space: pre-wrap;'>";
        if ($beforeImageSection) {
            echo htmlspecialchars($beforeImageSection);
        } else {
            echo htmlspecialchars(substr($originalContent, 0, 500)) . "\n...(truncated)";
        }
        echo "</pre>\n";
        echo "</div>\n";
        echo "<div>\n";
        echo "<h5>✅ After (Fixed):</h5>\n";
        echo "<pre style='background: #d4edda; padding: 10px; font-size: 11px; white-space: pre-wrap;'>";
        if ($afterImageSection) {
            echo htmlspecialchars($afterImageSection);
        } else {
            echo htmlspecialchars(substr($fixedContent, 0, 500)) . "\n...(truncated)";
        }
        echo "</pre>\n";
        echo "</div>\n";
        echo "</div>\n";
        
        echo "<h4>🧪 IMMEDIATE TEST REQUIRED:</h4>\n";
        echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7;'>\n";
        echo "<p style='font-size: 18px; color: #856404;'><strong>🔄 REFRESH THE TEMPLATE PREVIEW NOW!</strong></p>\n";
        echo "<ol>\n";
        echo "<li><strong>Go back to the template preview page</strong></li>\n";
        echo "<li><strong>Press F5 or Ctrl+R to refresh</strong></li>\n";
        echo "<li><strong>You should now see Sandra Stern's actual image</strong> (circular photo with styling)</li>\n";
        echo "<li><strong>No more '&lt;Sandra Stern' text</strong></li>\n";
        echo "</ol>\n";
        echo "</div>\n";
        
        echo "<h4>📝 What Was Fixed:</h4>\n";
        echo "<ul>\n";
        echo "<li>✅ Removed nested &lt;img src=\"&lt;img src=\" tags</li>\n";
        echo "<li>✅ Fixed wrong placeholder usage</li>\n";
        echo "<li>✅ Used correct {member_image} or {birthday_member_photo_url} placeholders</li>\n";
        echo "<li>✅ Cleaned up malformed HTML</li>\n";
        echo "<li>✅ Ensured proper image rendering</li>\n";
        echo "</ul>\n";
        
        echo "</div>\n";
    } else {
        echo "<p style='color: red; font-weight: bold;'>❌ Failed to update template in database!</p>\n";
    }
} else {
    echo "<div style='background: #e7f3ff; padding: 15px; border: 1px solid #b3d9ff;'>\n";
    echo "<h4>ℹ️ No Nested IMG Issues Found</h4>\n";
    echo "<p>The template doesn't appear to have the nested img tag issue.</p>\n";
    echo "<p>Let me check for other potential issues...</p>\n";
    
    // Show current image-related content
    if (preg_match_all('/.*(?:member|image|photo).*(?:src|alt|style).*/i', $content, $imageLines)) {
        echo "<h5>🔍 Current Image-Related Lines:</h5>\n";
        foreach ($imageLines[0] as $line) {
            echo "<p><code>" . htmlspecialchars($line) . "</code></p>\n";
        }
    }
    
    echo "</div>\n";
}

echo "<div style='background: #e7f3ff; padding: 15px; border: 1px solid #b3d9ff; margin: 20px 0;'>\n";
echo "<h4>🎯 Expected Result:</h4>\n";
echo "<p><strong>After this fix, you should see:</strong></p>\n";
echo "<ul>\n";
echo "<li>✅ Sandra Stern's circular photo (not text)</li>\n";
echo "<li>✅ Proper image styling and borders</li>\n";
echo "<li>✅ Correct alt text</li>\n";
echo "<li>❌ No more nested or broken img tags</li>\n";
echo "</ul>\n";
echo "</div>\n";

?>
