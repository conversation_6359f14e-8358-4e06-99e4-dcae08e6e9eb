<?php
require_once 'config.php';

$stmt = $pdo->prepare('SELECT id, template_name, content FROM email_templates WHERE id = 47');
$stmt->execute();
$template = $stmt->fetch();

if ($template) {
    echo "Template: {$template['template_name']}\n\n";
    
    // Look for all placeholders
    if (preg_match_all('/\{[^}]+\}/', $template['content'], $matches)) {
        echo "All placeholders found:\n";
        $placeholders = array_unique($matches[0]);
        foreach ($placeholders as $placeholder) {
            echo "- $placeholder\n";
        }
    }
    
    echo "\n";
    
    // Look for the specific area where the image URL was found in our debug
    if (strpos($template['content'], 'class="photo"') !== false) {
        $pos = strpos($template['content'], 'class="photo"');
        $start = max(0, $pos - 50);
        $length = 300;
        $context = substr($template['content'], $start, $length);
        echo "Photo div context:\n";
        echo $context . "\n";
    }
}
?>
