<?php
// Include the configuration file
require_once '../config.php';

// Include the session manager first (it will handle session start)
require_once '../admin/includes/session-manager.php';

// After session is started and configured, check if already logged in
if (isset($_SESSION['admin_id'])) {
    header("Location: dashboard.php");
    exit();
}

// Include the SecurityManager class
require_once '../classes/SecurityManager.php';

// Database connection - using the connection from config.php
$conn = $pdo;

// Initialize SecurityManager
$security = new SecurityManager($conn);

// Set security headers
$security->setSecurityHeaders();

$error = '';
$lockoutMessage = '';
$timeoutMessage = '';

// Check for timeout message
if (isset($_GET['timeout']) && $_GET['timeout'] == 1) {
    $timeoutMessage = "Your session has expired due to inactivity. Please login again.";
}

// Process login
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Validate CSRF token
    if (!isset($_POST['csrf_token']) || !$security->validateCSRFToken($_POST['csrf_token'])) {
        $error = "Invalid form submission. Please try again.";
    } else {
        $username = $security->sanitizeInput($_POST['username'], 'text');
        $password = $_POST['password']; // Don't sanitize passwords
        
        // Check if account is locked
        if ($security->isAccountLocked($username)) {
            $lockoutMessage = "This account has been temporarily locked due to multiple failed login attempts. Please try again later or contact an administrator.";
        } else {
            // Retrieve admin with matching username
            $stmt = $conn->prepare("SELECT id, username, password, full_name FROM admins WHERE username = ?");
            $stmt->execute([$username]);
            
            if ($admin = $stmt->fetch()) {
                // Verify password
                if ($security->verifyPassword($password, $admin['password'])) {
                    // Password is correct, reset failed attempts if any
                    $security->resetFailedAttempts($username);
                    
                    // Set session variables
                    $_SESSION['admin_id'] = $admin['id'];
                    $_SESSION['admin_name'] = $admin['full_name'];
                    $_SESSION['admin_username'] = $admin['username'];
                    
                    // Log successful login
                    $security->logSecurityEvent('Successful login', [
                        'admin_id' => $admin['id'],
                        'username' => $admin['username']
                    ]);
                    
                    // Update last login information
                    $stmt = $conn->prepare("UPDATE admins SET last_login_at = NOW(), last_login_ip = ? WHERE id = ?");
                    $stmt->execute([$_SERVER['REMOTE_ADDR'] ?? 'unknown', $admin['id']]);
                    
                    // Check if 2FA is enabled for this admin
                    $stmt = $conn->prepare("SELECT id FROM admin_2fa WHERE admin_id = ? AND is_enabled = 1");
                    $stmt->execute([$admin['id']]);
                    
                    if ($stmt->fetch()) {
                        // 2FA is enabled, set session flag and redirect to 2FA verification
                        $_SESSION['2fa_pending'] = true;
                        $_SESSION['2fa_admin_id'] = $admin['id'];
                        
                        header("Location: verify_2fa.php");
                        exit();
                    } else {
                        // 2FA not enabled, redirect to dashboard
                        header("Location: dashboard.php");
                        exit();
                    }
                } else {
                    // Password is incorrect, record failed attempt
                    $security->recordFailedAttempt($username);
                    $error = "Invalid password. Please try again.";
                }
            } else {
                // Username not found, but still record the attempt to prevent username enumeration
                $security->recordFailedAttempt($username);
                $error = "Invalid username or password. Please try again.";
            }
        }
    }
}

// Close the database connection
$conn = null;
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login - <?php echo get_organization_name(); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #4f46e5;
            --primary-hover: #4338ca;
            --secondary-color: #6366f1;
            --success-color: #10b981;
            --danger-color: #ef4444;
            --warning-color: #f59e0b;
            --dark-color: #1f2937;
            --light-color: #f8fafc;
            --border-color: #e5e7eb;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            position: relative;
            overflow-x: hidden;
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="10" cy="60" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="90" cy="40" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
        }

        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 24px;
            padding: 48px 40px;
            width: 100%;
            max-width: 440px;
            box-shadow: var(--shadow-xl);
            position: relative;
            animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .login-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .church-logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: var(--shadow-lg);
            transition: all 0.3s ease;
        }

        .church-logo:hover {
            transform: translateY(-2px) scale(1.05);
            box-shadow: var(--shadow-xl);
        }

        .church-logo img {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            object-fit: cover;
        }

        .church-logo i {
            font-size: 32px;
            color: white;
        }

        .login-title {
            font-size: 28px;
            font-weight: 700;
            color: var(--dark-color);
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
        }

        .login-title i {
            color: var(--primary-color);
            font-size: 24px;
        }

        .login-subtitle {
            color: #6b7280;
            font-size: 16px;
            font-weight: 400;
        }

        .form-floating {
            margin-bottom: 24px;
            position: relative;
        }

        .form-floating input {
            border: 2px solid var(--border-color);
            border-radius: 12px;
            padding: 16px 20px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.8);
        }

        .form-floating input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
            background: white;
        }

        .form-floating label {
            color: #6b7280;
            font-weight: 500;
        }

        .btn-login {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            border-radius: 12px;
            padding: 16px 24px;
            font-size: 16px;
            font-weight: 600;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-login::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn-login:hover::before {
            left: 100%;
        }

        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .btn-login:active {
            transform: translateY(0);
        }

        .alert {
            border: none;
            border-radius: 12px;
            padding: 16px 20px;
            margin-bottom: 24px;
            font-weight: 500;
        }

        .alert-danger {
            background: rgba(239, 68, 68, 0.1);
            color: var(--danger-color);
            border-left: 4px solid var(--danger-color);
        }

        .alert-warning {
            background: rgba(245, 158, 11, 0.1);
            color: var(--warning-color);
            border-left: 4px solid var(--warning-color);
        }

        .divider {
            display: flex;
            align-items: center;
            margin: 32px 0;
            color: #9ca3af;
            font-size: 14px;
        }

        .divider::before,
        .divider::after {
            content: '';
            flex: 1;
            height: 1px;
            background: var(--border-color);
        }

        .divider span {
            padding: 0 16px;
            background: rgba(255, 255, 255, 0.95);
        }

        .quick-links {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            margin-top: 32px;
        }

        .quick-link {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            padding: 12px 16px;
            border: 2px solid var(--border-color);
            border-radius: 12px;
            text-decoration: none;
            color: #6b7280;
            font-weight: 500;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.5);
        }

        .quick-link:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
            background: rgba(79, 70, 229, 0.05);
            transform: translateY(-1px);
        }

        .quick-link i {
            font-size: 16px;
        }

        .forgot-password-link {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            margin-top: 16px;
        }

        .forgot-password-link:hover {
            color: var(--primary-hover);
            text-decoration: underline;
        }

        .loading-spinner {
            display: none;
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .btn-login.loading .loading-spinner {
            display: inline-block;
        }

        .btn-login.loading .btn-text {
            display: none;
        }

        @media (max-width: 480px) {
            .login-container {
                padding: 32px 24px;
                margin: 16px;
            }

            .quick-links {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <div class="church-logo">
                <?php
                // Check if church logo exists
                $logoPath = '../assets/images/default-logo.png';
                if (file_exists(__DIR__ . '/' . $logoPath)):
                ?>
                    <img src="<?php echo $logoPath; ?>" alt="<?php echo get_organization_name(); ?> Logo">
                <?php else: ?>
                    <i class="fas fa-church"></i>
                <?php endif; ?>
            </div>
            <h1 class="login-title">
                <i class="fas fa-shield-alt"></i>
                Admin Login
            </h1>
            <p class="login-subtitle"><?php echo get_organization_name(); ?></p>
        </div>

        <?php if (!empty($error)): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?php echo $error; ?>
            </div>
        <?php endif; ?>

        <?php if (!empty($lockoutMessage)): ?>
            <div class="alert alert-warning">
                <i class="fas fa-lock me-2"></i>
                <?php echo $lockoutMessage; ?>
            </div>
        <?php elseif (!empty($timeoutMessage)): ?>
            <div class="alert alert-warning">
                <i class="fas fa-clock me-2"></i>
                <?php echo $timeoutMessage; ?>
            </div>
        <?php else: ?>
            <form method="POST" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" id="loginForm">
                <?php echo $security->generateCSRFInput(); ?>

                <div class="form-floating">
                    <input type="text" class="form-control" id="username" name="username" placeholder="Username" required>
                    <label for="username">
                        <i class="fas fa-user me-2"></i>Username
                    </label>
                </div>

                <div class="form-floating">
                    <input type="password" class="form-control" id="password" name="password" placeholder="Password" required>
                    <label for="password">
                        <i class="fas fa-lock me-2"></i>Password
                    </label>
                </div>

                <button type="submit" class="btn-login" id="loginBtn">
                    <div class="loading-spinner"></div>
                    <span class="btn-text">
                        <i class="fas fa-sign-in-alt me-2"></i>
                        Sign In
                    </span>
                </button>

                <div class="text-center">
                    <a href="forgot_password.php" class="forgot-password-link">
                        <i class="fas fa-key"></i>
                        Forgot Password?
                    </a>
                </div>
            </form>

            <div class="divider">
                <span>Quick Access</span>
            </div>

            <div class="quick-links">
                <a href="../user/login.php" class="quick-link">
                    <i class="fas fa-users"></i>
                    Member Login
                </a>
                <a href="../index.php" class="quick-link">
                    <i class="fas fa-home"></i>
                    Homepage
                </a>
            </div>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Enhanced login form interactions
        document.addEventListener('DOMContentLoaded', function() {
            const loginForm = document.getElementById('loginForm');
            const loginBtn = document.getElementById('loginBtn');
            const usernameInput = document.getElementById('username');
            const passwordInput = document.getElementById('password');

            // Add loading state on form submission
            if (loginForm) {
                loginForm.addEventListener('submit', function(e) {
                    loginBtn.classList.add('loading');
                    loginBtn.disabled = true;

                    // Re-enable button after 10 seconds as fallback
                    setTimeout(() => {
                        loginBtn.classList.remove('loading');
                        loginBtn.disabled = false;
                    }, 10000);
                });
            }

            // Enhanced input focus effects
            const inputs = document.querySelectorAll('.form-floating input');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.style.transform = 'translateY(-2px)';
                    this.parentElement.style.transition = 'transform 0.3s ease';
                });

                input.addEventListener('blur', function() {
                    this.parentElement.style.transform = 'translateY(0)';
                });
            });

            // Auto-focus username field
            if (usernameInput) {
                usernameInput.focus();
            }

            // Add ripple effect to buttons
            const buttons = document.querySelectorAll('.btn-login, .quick-link');
            buttons.forEach(button => {
                button.addEventListener('click', function(e) {
                    const ripple = document.createElement('span');
                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    const x = e.clientX - rect.left - size / 2;
                    const y = e.clientY - rect.top - size / 2;

                    ripple.style.width = ripple.style.height = size + 'px';
                    ripple.style.left = x + 'px';
                    ripple.style.top = y + 'px';
                    ripple.classList.add('ripple');

                    this.appendChild(ripple);

                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });

            // Keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                // Alt + H for homepage
                if (e.altKey && e.key === 'h') {
                    e.preventDefault();
                    window.location.href = '../index.php';
                }

                // Alt + M for member login
                if (e.altKey && e.key === 'm') {
                    e.preventDefault();
                    window.location.href = '../user/login.php';
                }
            });

            // Add floating particles effect
            createFloatingParticles();
        });

        // Create subtle floating particles
        function createFloatingParticles() {
            const particleCount = 15;
            const body = document.body;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.style.cssText = `
                    position: fixed;
                    width: 4px;
                    height: 4px;
                    background: rgba(255, 255, 255, 0.3);
                    border-radius: 50%;
                    pointer-events: none;
                    z-index: -1;
                    animation: float ${5 + Math.random() * 10}s infinite linear;
                    left: ${Math.random() * 100}vw;
                    top: ${Math.random() * 100}vh;
                    animation-delay: ${Math.random() * 5}s;
                `;
                body.appendChild(particle);
            }
        }

        // Add CSS for ripple and particle effects
        const style = document.createElement('style');
        style.textContent = `
            .ripple {
                position: absolute;
                border-radius: 50%;
                background: rgba(255, 255, 255, 0.3);
                transform: scale(0);
                animation: ripple-animation 0.6s linear;
                pointer-events: none;
            }

            @keyframes ripple-animation {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }

            @keyframes float {
                0%, 100% {
                    transform: translateY(0px) rotate(0deg);
                    opacity: 0.3;
                }
                50% {
                    transform: translateY(-20px) rotate(180deg);
                    opacity: 0.8;
                }
            }

            .quick-link {
                position: relative;
                overflow: hidden;
            }

            .btn-login {
                position: relative;
                overflow: hidden;
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>