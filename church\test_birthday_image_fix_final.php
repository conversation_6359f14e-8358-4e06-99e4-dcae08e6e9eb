<?php
/**
 * Final comprehensive test to verify birthday image fix is working
 */

require_once 'config.php';

echo "<h2>Final Birthday Image Fix Verification</h2>\n";

// Test 1: Verify template selection is using proper birthday templates
echo "<h3>Test 1: Template Selection</h3>\n";

$birthdayTemplate = getRandomTemplateForType('birthday');
if ($birthdayTemplate) {
    echo "✅ Birthday template selected: " . htmlspecialchars($birthdayTemplate['template_name']) . " (ID: {$birthdayTemplate['id']})\n";
    
    // Check if this template has proper image placeholders
    $hasMemberImage = strpos($birthdayTemplate['content'], '{member_image}') !== false;
    $hasBirthdayMemberImage = strpos($birthdayTemplate['content'], '{birthday_member_image}') !== false;
    
    echo "<br>Has {member_image}: " . ($hasMemberImage ? 'YES ✅' : 'NO ❌') . "\n";
    echo "<br>Has {birthday_member_image}: " . ($hasBirthdayMemberImage ? 'YES ✅' : 'NO ❌') . "\n";
    
    // Check for image tags
    preg_match_all('/<img[^>]+src=["\']([^"\']+)["\'][^>]*>/i', $birthdayTemplate['content'], $imgMatches);
    if (!empty($imgMatches[1])) {
        echo "<br>Image sources in template:\n";
        foreach ($imgMatches[1] as $src) {
            echo "<br>  - " . htmlspecialchars($src) . "\n";
        }
    }
} else {
    echo "❌ No birthday template found!\n";
}

// Test 2: Verify member data preparation
echo "<h3>Test 2: Member Data Preparation</h3>\n";

// Get a birthday member
$stmt = $pdo->prepare("
    SELECT * FROM members 
    WHERE image_path IS NOT NULL AND image_path != '' 
    ORDER BY id LIMIT 1
");
$stmt->execute();
$birthdayMember = $stmt->fetch(PDO::FETCH_ASSOC);

if ($birthdayMember) {
    echo "Birthday member: " . htmlspecialchars($birthdayMember['full_name']) . "\n";
    echo "<br>Image path: " . htmlspecialchars($birthdayMember['image_path']) . "\n";
    
    // Simulate the member data preparation from send_birthday_reminders.php
    $birthdayMemberPhotoUrl = 'http://localhost/campaign/church/' . $birthdayMember['image_path'];
    $memberImageUrl = $birthdayMemberPhotoUrl;
    
    $memberData = [
        'full_name' => 'Test Recipient',
        'email' => '<EMAIL>',
        'birthday_member_full_name' => $birthdayMember['full_name'],
        'birthday_member_photo_url' => $birthdayMemberPhotoUrl,
        'birthday_member_image' => $birthdayMemberPhotoUrl,
        'member_image' => $memberImageUrl,
        'member_image_url' => $memberImageUrl,
        '_is_birthday_notification' => true,
        '_original_image_path' => $birthdayMember['image_path'],
        'template_name' => $birthdayTemplate['template_name'] ?? 'Test Template'
    ];
    
    echo "<br><br>Member data prepared:\n";
    echo "<br>birthday_member_photo_url: " . htmlspecialchars($memberData['birthday_member_photo_url']) . "\n";
    echo "<br>member_image: " . htmlspecialchars($memberData['member_image']) . "\n";
    echo "<br>_is_birthday_notification: " . ($memberData['_is_birthday_notification'] ? 'true' : 'false') . "\n";
    
    // Test 3: Template processing
    echo "<h3>Test 3: Template Processing</h3>\n";
    
    if ($birthdayTemplate) {
        $processedContent = replaceTemplatePlaceholders($birthdayTemplate['content'], $memberData);
        
        // Check if {member_image} was replaced
        $memberImageReplaced = strpos($processedContent, '{member_image}') === false;
        echo "✅ {member_image} placeholder replaced: " . ($memberImageReplaced ? 'YES' : 'NO') . "\n";
        
        // Check what it was replaced with
        preg_match_all('/<img[^>]+src=["\']([^"\']+)["\'][^>]*>/i', $processedContent, $processedImgMatches);
        if (!empty($processedImgMatches[1])) {
            echo "<br>Image sources after processing:\n";
            foreach ($processedImgMatches[1] as $src) {
                echo "<br>  - " . htmlspecialchars($src) . "\n";
                if (strpos($src, $birthdayMember['image_path']) !== false) {
                    echo " ✅ Contains birthday member image path\n";
                }
            }
        }
        
        // Test 4: Birthday notification detection
        echo "<h3>Test 4: Birthday Notification Detection</h3>\n";
        
        // Simulate the detection logic from config.php
        $isBirthdayNotification = false;
        
        // Method 1: Check explicit flag
        if (isset($memberData['_is_birthday_notification']) && $memberData['_is_birthday_notification']) {
            $isBirthdayNotification = true;
            echo "✅ Birthday notification detected via explicit flag\n";
        }
        // Method 2: Check template name
        else if (isset($memberData['template_name']) &&
                (stripos($memberData['template_name'], 'birthday notification') !== false ||
                 stripos($memberData['template_name'], 'birthday template') !== false ||
                 stripos($memberData['template_name'], 'birthday reminder') !== false)) {
            $isBirthdayNotification = true;
            echo "✅ Birthday notification detected via template name\n";
        }
        // Method 3: Check for birthday member data
        else if (isset($memberData['birthday_member_name']) ||
                 isset($memberData['birthday_member_full_name']) ||
                 isset($memberData['birthday_member_photo_url'])) {
            $isBirthdayNotification = true;
            echo "✅ Birthday notification detected via birthday member data\n";
        }
        
        if (!$isBirthdayNotification) {
            echo "❌ Birthday notification NOT detected\n";
        }
        
        echo "<h3>Test 5: Image Embedding Simulation</h3>\n";
        
        if ($isBirthdayNotification && isset($memberData['birthday_member_photo_url'])) {
            echo "✅ Birthday member photo URL found: " . htmlspecialchars($memberData['birthday_member_photo_url']) . "\n";
            
            // Check if the image file exists
            $imagePath = str_replace('http://localhost/campaign/church/', '', $memberData['birthday_member_photo_url']);
            $localPath = __DIR__ . '/' . $imagePath;
            
            if (file_exists($localPath)) {
                echo "<br>✅ Image file exists: " . htmlspecialchars($localPath) . "\n";
                echo "<br>✅ Birthday member image embedding would work!\n";
            } else {
                echo "<br>❌ Image file does not exist: " . htmlspecialchars($localPath) . "\n";
            }
        } else {
            echo "❌ Birthday member photo URL not found or not a birthday notification\n";
        }
    }
} else {
    echo "❌ No member with image found for testing\n";
}

echo "<h3>Summary</h3>\n";
echo "The birthday image fix includes:\n";
echo "1. ✅ Fixed getRandomTemplateForType() to use is_birthday_template flag\n";
echo "2. ✅ Enhanced image embedding logic to handle {member_image} for birthday notifications\n";
echo "3. ✅ Added _is_birthday_notification flag for proper detection\n";
echo "4. ✅ Ensured birthday member image URL is set in member data\n";
?>
