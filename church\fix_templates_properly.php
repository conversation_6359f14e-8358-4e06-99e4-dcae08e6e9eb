<?php
/**
 * Fix birthday notification templates to use proper img tags for {member_image}
 */

require_once 'config.php';

echo "<h2>Fixing Birthday Notification Templates - Proper Fix</h2>\n";

try {
    // Get all birthday notification templates
    $stmt = $pdo->prepare('SELECT id, template_name, content FROM email_templates WHERE template_name LIKE "%notification%"');
    $stmt->execute();
    $templates = $stmt->fetchAll();
    
    foreach ($templates as $template) {
        echo "<h3>Processing Template: {$template['template_name']} (ID: {$template['id']})</h3>\n";
        
        $content = $template['content'];
        $originalContent = $content;
        
        // Look for the photo div pattern with {member_image} and replace it with proper img tag
        $pattern = '/<div[^>]*class=["\']photo["\'][^>]*>\s*\{member_image\}\s*<\/div>/i';
        
        if (preg_match($pattern, $content)) {
            echo "<p>✅ Found photo div with {member_image} placeholder</p>\n";
            
            // Replace with proper img tag
            $replacement = '<div class="photo"><img src="{member_image}" alt="{birthday_member_full_name}" style="width: 100%; height: 100%; object-fit: cover;"></div>';
            
            $content = preg_replace($pattern, $replacement, $content);
            
            echo "<p>✅ Replaced photo div content with proper img tag</p>\n";
        } else {
            // Look for {member_image} in other contexts
            if (strpos($content, '{member_image}') !== false) {
                echo "<p>⚠️ Found {member_image} placeholder but not in expected photo div format</p>\n";
                
                // Show context around the placeholder
                $pos = strpos($content, '{member_image}');
                $start = max(0, $pos - 100);
                $length = 200;
                $context = substr($content, $start, $length);
                echo "<p><strong>Context:</strong></p>\n";
                echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd;'>";
                echo htmlspecialchars($context);
                echo "</pre>\n";
                
                // Try a more specific replacement for the photo div
                $content = preg_replace(
                    '/<div[^>]*class=["\']photo["\'][^>]*>.*?\{member_image\}.*?<\/div>/s',
                    '<div class="photo"><img src="{member_image}" alt="{birthday_member_full_name}" style="width: 100%; height: 100%; object-fit: cover;"></div>',
                    $content
                );
                
                echo "<p>✅ Applied flexible replacement for photo div</p>\n";
            } else {
                echo "<p>❌ No {member_image} placeholder found</p>\n";
            }
        }
        
        // Update the template if content changed
        if ($content !== $originalContent) {
            $updateStmt = $pdo->prepare('UPDATE email_templates SET content = ? WHERE id = ?');
            $result = $updateStmt->execute([$content, $template['id']]);
            
            if ($result) {
                echo "<p style='color: green;'>✅ Template updated successfully</p>\n";
                
                // Show what changed
                echo "<p><strong>Before:</strong></p>\n";
                $beforePos = strpos($originalContent, 'class="photo"');
                if ($beforePos !== false) {
                    $beforeContext = substr($originalContent, $beforePos, 200);
                    echo "<pre style='background: #ffe6e6; padding: 10px; border: 1px solid #ff9999;'>";
                    echo htmlspecialchars($beforeContext);
                    echo "</pre>\n";
                }
                
                echo "<p><strong>After:</strong></p>\n";
                $afterPos = strpos($content, 'class="photo"');
                if ($afterPos !== false) {
                    $afterContext = substr($content, $afterPos, 200);
                    echo "<pre style='background: #e6ffe6; padding: 10px; border: 1px solid #99ff99;'>";
                    echo htmlspecialchars($afterContext);
                    echo "</pre>\n";
                }
            } else {
                echo "<p style='color: red;'>❌ Failed to update template</p>\n";
            }
        } else {
            echo "<p>ℹ️ No changes needed for this template</p>\n";
        }
        
        echo "<hr>\n";
    }
    
    echo "<h3>Summary</h3>\n";
    echo "<p>All birthday notification templates have been processed to use proper img tags.</p>\n";
    echo "<p><strong>Next steps:</strong></p>\n";
    echo "<ul>\n";
    echo "<li>Test sending a birthday notification to verify images display correctly</li>\n";
    echo "<li>Check that the URL replacement now works properly</li>\n";
    echo "<li>Verify that images appear in email clients instead of empty circles</li>\n";
    echo "</ul>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}
?>
