<?php
require_once 'config.php';

echo "<h2>Critical Analysis: Upcoming Birthday Templates</h2>\n";

global $pdo;

// Find the specific problematic templates
$templateNames = [
    'Member Upcoming Birthday Notification 1',
    'Member Upcoming Birthday Notification 2', 
    'Member Upcoming Birthday Notification 3'
];

foreach ($templateNames as $templateName) {
    $stmt = $pdo->prepare("SELECT * FROM email_templates WHERE template_name = ?");
    $stmt->execute([$templateName]);
    $template = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$template) {
        echo "<div style='border: 2px solid #dc3545; margin: 20px 0; padding: 15px;'>\n";
        echo "<h3>❌ Template Not Found: " . htmlspecialchars($templateName) . "</h3>\n";
        echo "</div>\n";
        continue;
    }
    
    echo "<div style='border: 2px solid #dc3545; margin: 20px 0; padding: 15px;'>\n";
    echo "<h3>🔍 Analyzing: " . htmlspecialchars($template['template_name']) . " (ID: {$template['id']})</h3>\n";
    
    $content = $template['content'];
    
    // Check for the specific broken pattern mentioned
    if (strpos($content, 'Sandra Stern') !== false) {
        echo "<p style='color: red; font-weight: bold;'>❌ CRITICAL: Contains hardcoded 'Sandra Stern' text!</p>\n";
        
        // Find the context around Sandra <PERSON>
        $pos = strpos($content, 'Sandra Stern');
        $start = max(0, $pos - 100);
        $length = 300;
        $context = substr($content, $start, $length);
        
        echo "<h4>🔍 Context around 'Sandra Stern':</h4>\n";
        echo "<pre style='background: #fff3cd; padding: 10px; border: 1px solid #ffeaa7; white-space: pre-wrap;'>" . htmlspecialchars($context) . "</pre>\n";
    }
    
    // Look for broken image patterns
    $brokenPatterns = [
        '/[A-Za-z\s]+"[^>]*alt="[^"]*"[^>]*style="[^"]*"[^>]*>/',
        '/Sandra Stern"[^>]*>/',
        '/Member"[^>]*>/',
        '/"[^"]*alt="[^"]*"[^>]*style="[^"]*width[^>]*>/'
    ];
    
    foreach ($brokenPatterns as $i => $pattern) {
        if (preg_match_all($pattern, $content, $matches)) {
            echo "<h4>🚨 Broken Pattern " . ($i + 1) . " Found:</h4>\n";
            foreach ($matches[0] as $match) {
                echo "<p style='background: #f8d7da; padding: 10px; border: 1px solid #f5c6cb;'><code>" . htmlspecialchars($match) . "</code></p>\n";
            }
        }
    }
    
    // Look for proper image placeholders
    $imagePlaceholders = [
        '{member_image}',
        '{birthday_member_image}',
        '{birthday_member_photo_url}',
        '{image_path}',
        '{profile_photo}'
    ];
    
    $hasValidPlaceholder = false;
    foreach ($imagePlaceholders as $placeholder) {
        if (strpos($content, $placeholder) !== false) {
            echo "<p style='color: green;'>✅ Found valid placeholder: <code>$placeholder</code></p>\n";
            $hasValidPlaceholder = true;
        }
    }
    
    if (!$hasValidPlaceholder) {
        echo "<p style='color: red; font-weight: bold;'>❌ NO VALID IMAGE PLACEHOLDERS FOUND!</p>\n";
    }
    
    // Look for img tags
    if (preg_match_all('/<img[^>]*>/i', $content, $matches)) {
        echo "<h4>🖼️ Image Tags Found:</h4>\n";
        foreach ($matches[0] as $imgTag) {
            echo "<div style='background: #e2e3e5; padding: 10px; margin: 5px 0; border-left: 4px solid #6c757d;'>\n";
            echo "<code>" . htmlspecialchars($imgTag) . "</code>\n";
            
            // Analyze the src attribute
            if (preg_match('/src="([^"]*)"/', $imgTag, $srcMatch)) {
                $src = $srcMatch[1];
                if (str_starts_with($src, '{') && str_ends_with($src, '}')) {
                    echo "<br><span style='color: green;'>✅ Uses placeholder: $src</span>\n";
                } else {
                    echo "<br><span style='color: red;'>❌ Hardcoded src: $src</span>\n";
                }
            }
            echo "</div>\n";
        }
    }
    
    // Show a portion of the template content
    echo "<h4>📄 Template Content Preview:</h4>\n";
    echo "<div style='background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; max-height: 300px; overflow-y: auto;'>\n";
    echo "<pre style='white-space: pre-wrap; font-size: 12px;'>" . htmlspecialchars(substr($content, 0, 1000)) . "...</pre>\n";
    echo "</div>\n";
    
    echo "</div>\n";
}

echo "<h3>🎯 Next Steps</h3>\n";
echo "<p>Based on this analysis, we need to:</p>\n";
echo "<ol>\n";
echo "<li>Remove hardcoded names like 'Sandra Stern' from templates</li>\n";
echo "<li>Replace broken image patterns with proper placeholders</li>\n";
echo "<li>Ensure image embedding works correctly</li>\n";
echo "<li>Test the preview system</li>\n";
echo "</ol>\n";

?>
