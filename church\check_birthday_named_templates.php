<?php
require_once 'config.php';

echo "Checking ALL templates with 'birthday' in their name...\n\n";

$stmt = $pdo->prepare("
    SELECT id, template_name, content, is_birthday_template
    FROM email_templates 
    WHERE template_name LIKE '%birthday%' OR template_name LIKE '%Birthday%'
    ORDER BY template_name
");
$stmt->execute();
$templates = $stmt->fetchAll(PDO::FETCH_ASSOC);

foreach ($templates as $template) {
    echo "Template: " . $template['template_name'] . " (ID: " . $template['id'] . ")\n";
    echo "  is_birthday_template: " . ($template['is_birthday_template'] ? 'YES' : 'NO') . "\n";
    
    $hasBirthdayMemberImage = strpos($template['content'], '{birthday_member_image}') !== false;
    $hasMemberImage = strpos($template['content'], '{member_image}') !== false;
    
    echo "  Has {birthday_member_image}: " . ($hasBirthdayMemberImage ? 'YES' : 'NO') . "\n";
    echo "  Has {member_image}: " . ($hasMemberImage ? 'YES' : 'NO') . "\n";
    
    // Check for any image tags
    preg_match_all('/<img[^>]+src=["\']([^"\']+)["\'][^>]*>/i', $template['content'], $imgMatches);
    if (!empty($imgMatches[1])) {
        echo "  Image sources found:\n";
        foreach ($imgMatches[1] as $src) {
            echo "    - " . $src . "\n";
        }
    } else {
        echo "  ❌ NO IMAGE TAGS FOUND\n";
    }
    
    echo "\n" . str_repeat("-", 60) . "\n\n";
}

echo "Total templates with 'birthday' in name: " . count($templates) . "\n";
?>
