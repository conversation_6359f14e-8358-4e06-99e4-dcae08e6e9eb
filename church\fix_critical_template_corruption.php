<?php
require_once 'config.php';

echo "<h2>🔧 CRITICAL FIX: Template Corruption Repair</h2>\n";

global $pdo;

// Get all templates with the corruption pattern
$stmt = $pdo->prepare("
    SELECT id, template_name, content 
    FROM email_templates 
    WHERE content REGEXP '[A-Za-z ]+\"[^>]*alt=\"[^\"]*\"[^>]*style=\"[^\"]*\"[^>]*>'
    ORDER BY template_name
");
$stmt->execute();
$templates = $stmt->fetchAll(PDO::FETCH_ASSOC);

$fixedCount = 0;
$totalIssues = 0;

foreach ($templates as $template) {
    echo "<div style='border: 3px solid #dc3545; margin: 20px 0; padding: 20px;'>\n";
    echo "<h3>🔧 Fixing: " . htmlspecialchars($template['template_name']) . " (ID: {$template['id']})</h3>\n";
    
    $content = $template['content'];
    $originalContent = $content;
    $hasChanges = false;
    
    // Pattern 1: Fix broken image tags where name appears before attributes
    // Example: <PERSON>" alt="<PERSON>" style="width: 150px..."
    // Should be: <img src="{birthday_member_image}" alt="Sandra Stern" style="width: 150px..."
    
    $pattern1 = '/([A-Za-z\s]+)"(\s*alt="[^"]*")(\s*style="[^"]*"[^>]*>)/';
    if (preg_match_all($pattern1, $content, $matches, PREG_SET_ORDER)) {
        echo "<h4>🔍 Found " . count($matches) . " broken image patterns</h4>\n";
        
        foreach ($matches as $match) {
            $fullMatch = $match[0];
            $nameText = trim($match[1]);
            $altAttribute = $match[2];
            $styleAndRest = $match[3];
            
            echo "<div style='background: #fff3cd; padding: 10px; margin: 10px 0; border-left: 4px solid #ffc107;'>\n";
            echo "<p><strong>Broken:</strong> <code>" . htmlspecialchars($fullMatch) . "</code></p>\n";
            
            // Create the fixed version
            $fixed = '<img src="{birthday_member_image}"' . $altAttribute . $styleAndRest;
            echo "<p><strong>Fixed:</strong> <code>" . htmlspecialchars($fixed) . "</code></p>\n";
            
            // Apply the fix
            $content = str_replace($fullMatch, $fixed, $content);
            $hasChanges = true;
            $totalIssues++;
            echo "</div>\n";
        }
    }
    
    // Pattern 2: Fix any remaining broken patterns where img tag is malformed
    $pattern2 = '/([A-Za-z\s]+)"\s*(alt="[^"]*")\s*(style="[^"]*"[^>]*>)/';
    if (preg_match_all($pattern2, $content, $matches2, PREG_SET_ORDER)) {
        foreach ($matches2 as $match) {
            if (strpos($match[0], '<img') === false) { // Only fix if not already an img tag
                $fullMatch = $match[0];
                $altAttribute = $match[2];
                $styleAndRest = $match[3];
                
                $fixed = '<img src="{birthday_member_image}" ' . $altAttribute . ' ' . $styleAndRest;
                $content = str_replace($fullMatch, $fixed, $content);
                $hasChanges = true;
            }
        }
    }
    
    // Pattern 3: Fix any standalone image attributes without img tag
    $pattern3 = '/"(\s*alt="[^"]*")(\s*style="[^"]*width[^"]*"[^>]*>)/';
    if (preg_match($pattern3, $content) && strpos($content, '<img') === false) {
        $content = preg_replace($pattern3, '<img src="{birthday_member_image}"$1$2', $content);
        $hasChanges = true;
    }
    
    // Verify we have proper image placeholders
    $hasImagePlaceholder = false;
    $imagePlaceholders = ['{birthday_member_image}', '{member_image}', '{birthday_member_photo_url}'];
    foreach ($imagePlaceholders as $placeholder) {
        if (strpos($content, $placeholder) !== false) {
            $hasImagePlaceholder = true;
            break;
        }
    }
    
    if (!$hasImagePlaceholder && $hasChanges) {
        echo "<p style='color: orange;'>⚠️ Warning: No image placeholders found after fix. Adding default placeholder.</p>\n";
        // If we made changes but still no placeholder, ensure we have one
        $content = str_replace('src=""', 'src="{birthday_member_image}"', $content);
    }
    
    if ($hasChanges) {
        // Update the template in database
        $updateStmt = $pdo->prepare("UPDATE email_templates SET content = ? WHERE id = ?");
        $result = $updateStmt->execute([$content, $template['id']]);
        
        if ($result) {
            echo "<p style='color: green; font-weight: bold; font-size: 16px;'>✅ TEMPLATE FIXED SUCCESSFULLY!</p>\n";
            $fixedCount++;
            
            // Show before/after comparison
            echo "<h4>📋 Before/After Comparison:</h4>\n";
            echo "<div style='display: grid; grid-template-columns: 1fr 1fr; gap: 20px;'>\n";
            echo "<div>\n";
            echo "<h5 style='color: red;'>❌ Before (Broken):</h5>\n";
            echo "<pre style='background: #f8d7da; padding: 10px; font-size: 11px; max-height: 150px; overflow-y: auto; white-space: pre-wrap;'>" . htmlspecialchars(substr($originalContent, 0, 800)) . "</pre>\n";
            echo "</div>\n";
            echo "<div>\n";
            echo "<h5 style='color: green;'>✅ After (Fixed):</h5>\n";
            echo "<pre style='background: #d4edda; padding: 10px; font-size: 11px; max-height: 150px; overflow-y: auto; white-space: pre-wrap;'>" . htmlspecialchars(substr($content, 0, 800)) . "</pre>\n";
            echo "</div>\n";
            echo "</div>\n";
        } else {
            echo "<p style='color: red; font-weight: bold;'>❌ FAILED TO UPDATE TEMPLATE!</p>\n";
        }
    } else {
        echo "<p style='color: blue;'>ℹ️ No corruption found in this template.</p>\n";
    }
    
    echo "</div>\n";
}

echo "<div style='border: 3px solid #28a745; margin: 20px 0; padding: 20px; background: #f8fff9;'>\n";
echo "<h3>🎉 REPAIR SUMMARY</h3>\n";
echo "<p><strong>Templates processed:</strong> " . count($templates) . "</p>\n";
echo "<p><strong>Templates fixed:</strong> $fixedCount</p>\n";
echo "<p><strong>Total issues resolved:</strong> $totalIssues</p>\n";

if ($fixedCount > 0) {
    echo "<h4>✅ What was fixed:</h4>\n";
    echo "<ul>\n";
    echo "<li>Corrupted image HTML where member names appeared as text</li>\n";
    echo "<li>Missing &lt;img src='' tags</li>\n";
    echo "<li>Proper placeholder insertion: <code>{birthday_member_image}</code></li>\n";
    echo "<li>Preserved alt text and styling attributes</li>\n";
    echo "</ul>\n";
    
    echo "<h4>🎯 Next Steps:</h4>\n";
    echo "<ol>\n";
    echo "<li>Test the template previews to ensure images show correctly</li>\n";
    echo "<li>Send test birthday notifications to verify email embedding</li>\n";
    echo "<li>Check that placeholder replacement works properly</li>\n";
    echo "</ol>\n";
} else {
    echo "<p style='color: orange;'>⚠️ No templates needed fixing, or the corruption pattern wasn't found.</p>\n";
}
echo "</div>\n";

?>
