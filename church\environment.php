<?php
/**
 * Environment Configuration
 * Dynamically detects environment and sets appropriate URLs
 */

// Prevent direct access
if (!defined('CONFIG_INCLUDED')) {
    die('Direct access not permitted');
}

// Auto-detect environment based on server characteristics
$environment = 'development'; // Default

if (isset($_SERVER['SERVER_NAME'])) {
    $serverName = $_SERVER['SERVER_NAME'];
    
    // Production environment detection
    if (strpos($serverName, 'freedomassemblydb.online') !== false ||
        strpos($serverName, 'localhost') === false && 
        strpos($serverName, '127.0.0.1') === false) {
        $environment = 'production';
    }
    
    // Staging environment detection
    if (strpos($serverName, 'staging') !== false || 
        strpos($serverName, 'test') !== false) {
        $environment = 'staging';
    }
}

// Dynamic URL detection
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
$host = $_SERVER['HTTP_HOST'] ?? 'localhost';
$scriptPath = dirname($_SERVER['SCRIPT_NAME'] ?? '');

// Clean up script path to get base path
$basePath = $scriptPath;
if (strpos($basePath, '/admin') !== false) {
    $basePath = dirname($basePath);
}
if (strpos($basePath, '/user') !== false) {
    $basePath = dirname($basePath);
}

// Construct base URL
$baseUrl = $protocol . '://' . $host . $basePath;

// Define environment-specific URLs
if ($environment === 'production') {
    // Production URLs - use freedomassemblydb.online
    if (!defined('SITE_URL')) {
        define('SITE_URL', 'https://freedomassemblydb.online/campaign/church');
    }
    if (!defined('BASE_URL')) {
        define('BASE_URL', 'https://freedomassemblydb.online/campaign/church');
    }
    if (!defined('ADMIN_URL')) {
        define('ADMIN_URL', 'https://freedomassemblydb.online/campaign/church/admin');
    }
    if (!defined('USER_URL')) {
        define('USER_URL', 'https://freedomassemblydb.online/campaign/church/user');
    }
    if (!defined('ASSETS_URL')) {
        define('ASSETS_URL', 'https://freedomassemblydb.online/campaign/church/assets');
    }
    if (!defined('UPLOADS_URL')) {
        define('UPLOADS_URL', 'https://freedomassemblydb.online/campaign/church/uploads');
    }
} else {
    // Development/Local URLs - dynamic detection
    if (!defined('SITE_URL')) {
        define('SITE_URL', $baseUrl);
    }
    if (!defined('BASE_URL')) {
        define('BASE_URL', $baseUrl);
    }
    if (!defined('ADMIN_URL')) {
        define('ADMIN_URL', $baseUrl . '/admin');
    }
    if (!defined('USER_URL')) {
        define('USER_URL', $baseUrl . '/user');
    }
    if (!defined('ASSETS_URL')) {
        define('ASSETS_URL', $baseUrl . '/assets');
    }
    if (!defined('UPLOADS_URL')) {
        define('UPLOADS_URL', $baseUrl . '/uploads');
    }
}

// Additional environment constants
if (!defined('ENVIRONMENT')) {
    define('ENVIRONMENT', $environment);
}

if (!defined('IS_PRODUCTION')) {
    define('IS_PRODUCTION', $environment === 'production');
}

if (!defined('IS_DEVELOPMENT')) {
    define('IS_DEVELOPMENT', $environment === 'development');
}

// Debug mode based on environment
if (!defined('DEBUG_MODE')) {
    define('DEBUG_MODE', $environment === 'development');
}

// Error reporting based on environment
if ($environment === 'production') {
    error_reporting(E_ERROR | E_WARNING | E_PARSE);
    ini_set('display_errors', 0);
    ini_set('log_errors', 1);
} else {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
    ini_set('log_errors', 1);
}

// Return environment for backward compatibility
return $environment;
?>
