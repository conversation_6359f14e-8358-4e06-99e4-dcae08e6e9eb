<?php
/**
 * Fix birthday notification templates to use proper img tags
 */

require_once 'config.php';

echo "<h2>Fixing Birthday Notification Templates</h2>\n";

try {
    // Get all birthday notification templates
    $stmt = $pdo->prepare('SELECT id, template_name, content FROM email_templates WHERE template_name LIKE "%notification%"');
    $stmt->execute();
    $templates = $stmt->fetchAll();
    
    foreach ($templates as $template) {
        echo "<h3>Processing Template: {$template['template_name']} (ID: {$template['id']})</h3>\n";
        
        $content = $template['content'];
        $originalContent = $content;
        
        // Look for the photo div pattern and replace it with proper img tag
        $pattern = '/<div[^>]*class=["\']photo["\'][^>]*>\s*\{birthday_member_photo_url\}\s*<\/div>/i';
        
        if (preg_match($pattern, $content)) {
            echo "<p>✅ Found photo div with birthday_member_photo_url placeholder</p>\n";
            
            // Replace with proper img tag
            $replacement = '<img src="{birthday_member_photo_url}" alt="{birthday_member_full_name}" style="width: 150px; height: 150px; border-radius: 50%; object-fit: cover; border: 4px solid #ddd; display: block; margin: 0 auto;">';
            
            $content = preg_replace($pattern, $replacement, $content);
            
            echo "<p>✅ Replaced photo div with proper img tag</p>\n";
        } else {
            // Look for other patterns
            if (strpos($content, '{birthday_member_photo_url}') !== false) {
                echo "<p>⚠️ Found birthday_member_photo_url placeholder but not in expected photo div format</p>\n";
                
                // Show context around the placeholder
                $pos = strpos($content, '{birthday_member_photo_url}');
                $start = max(0, $pos - 100);
                $length = 200;
                $context = substr($content, $start, $length);
                echo "<p><strong>Context:</strong></p>\n";
                echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd;'>";
                echo htmlspecialchars($context);
                echo "</pre>\n";
                
                // Try a more flexible replacement
                $content = str_replace(
                    '{birthday_member_photo_url}',
                    '<img src="{birthday_member_photo_url}" alt="{birthday_member_full_name}" style="width: 150px; height: 150px; border-radius: 50%; object-fit: cover; border: 4px solid #ddd; display: block; margin: 0 auto;">',
                    $content
                );
                
                echo "<p>✅ Replaced placeholder with img tag</p>\n";
            } else {
                echo "<p>❌ No birthday_member_photo_url placeholder found</p>\n";
            }
        }
        
        // Update the template if content changed
        if ($content !== $originalContent) {
            $updateStmt = $pdo->prepare('UPDATE email_templates SET content = ? WHERE id = ?');
            $result = $updateStmt->execute([$content, $template['id']]);
            
            if ($result) {
                echo "<p style='color: green;'>✅ Template updated successfully</p>\n";
            } else {
                echo "<p style='color: red;'>❌ Failed to update template</p>\n";
            }
        } else {
            echo "<p>ℹ️ No changes needed for this template</p>\n";
        }
        
        echo "<hr>\n";
    }
    
    echo "<h3>Summary</h3>\n";
    echo "<p>All birthday notification templates have been processed.</p>\n";
    echo "<p><strong>Next steps:</strong></p>\n";
    echo "<ul>\n";
    echo "<li>Test sending a birthday notification to verify images display correctly</li>\n";
    echo "<li>Check that the URL replacement now works properly</li>\n";
    echo "</ul>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}
?>
