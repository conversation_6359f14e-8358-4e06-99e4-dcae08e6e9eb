<?php
require_once 'config.php';

echo "Checking ALL birthday templates for image placeholders...\n\n";

$stmt = $pdo->prepare("
    SELECT id, template_name, content 
    FROM email_templates 
    WHERE is_birthday_template = 1
    ORDER BY template_name
");
$stmt->execute();
$templates = $stmt->fetchAll(PDO::FETCH_ASSOC);

$templatesWithImages = 0;
$templatesWithoutImages = 0;
$templatesNeedingFix = [];

foreach ($templates as $template) {
    echo "Template: " . $template['template_name'] . " (ID: " . $template['id'] . ")\n";
    
    $hasBirthdayMemberImage = strpos($template['content'], '{birthday_member_image}') !== false;
    $hasMemberImage = strpos($template['content'], '{member_image}') !== false;
    
    echo "  Has {birthday_member_image}: " . ($hasBirthdayMemberImage ? 'YES' : 'NO') . "\n";
    echo "  Has {member_image}: " . ($hasMemberImage ? 'YES' : 'NO') . "\n";
    
    // Check for any image tags
    preg_match_all('/<img[^>]+src=["\']([^"\']+)["\'][^>]*>/i', $template['content'], $imgMatches);
    if (!empty($imgMatches[1])) {
        echo "  Image sources found:\n";
        $hasProperImagePlaceholder = false;
        foreach ($imgMatches[1] as $src) {
            echo "    - " . $src . "\n";
            if (strpos($src, '{birthday_member_image}') !== false || strpos($src, '{member_image}') !== false) {
                $hasProperImagePlaceholder = true;
            }
        }
        
        if ($hasProperImagePlaceholder) {
            echo "  ✅ HAS PROPER IMAGE PLACEHOLDER\n";
            $templatesWithImages++;
        } else {
            echo "  ❌ NO PROPER IMAGE PLACEHOLDER IN IMG TAGS\n";
            $templatesWithoutImages++;
            $templatesNeedingFix[] = $template;
        }
    } else {
        echo "  ❌ NO IMAGE TAGS FOUND\n";
        $templatesWithoutImages++;
        $templatesNeedingFix[] = $template;
    }
    
    echo "\n" . str_repeat("-", 60) . "\n\n";
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "SUMMARY:\n";
echo "Templates with proper image placeholders: $templatesWithImages\n";
echo "Templates without proper image placeholders: $templatesWithoutImages\n";
echo "Total templates: " . count($templates) . "\n";

if (!empty($templatesNeedingFix)) {
    echo "\nTemplates that need fixing:\n";
    foreach ($templatesNeedingFix as $template) {
        echo "- " . $template['template_name'] . " (ID: " . $template['id'] . ")\n";
    }
}
?>
