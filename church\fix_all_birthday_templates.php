<?php
require_once 'config.php';

echo "<h2>🔧 FIX ALL BIRTHDAY UPCOMING REMINDER TEMPLATES</h2>\n";

global $pdo;

// Find all birthday upcoming reminder templates
$stmt = $pdo->prepare("SELECT * FROM email_templates WHERE template_name LIKE '%Member Upcoming Birthday Notification%' OR template_name LIKE '%Birthday Upcoming%' OR template_name LIKE '%upcoming birthday%' ORDER BY template_name");
$stmt->execute();
$templates = $stmt->fetchAll(PDO::FETCH_ASSOC);

if (empty($templates)) {
    echo "<p style='color: red;'>❌ No birthday upcoming reminder templates found!</p>\n";
    exit;
}

echo "<h3>📋 Found " . count($templates) . " Birthday Templates:</h3>\n";
foreach ($templates as $template) {
    echo "<p>• <strong>ID " . $template['id'] . ":</strong> " . htmlspecialchars($template['template_name']) . "</p>\n";
}

echo "<h3>🔍 Analyzing Each Template for Issues:</h3>\n";

$templatesNeedingFix = [];

foreach ($templates as $template) {
    echo "<div style='border: 2px solid #007bff; margin: 15px 0; padding: 15px;'>\n";
    echo "<h4>🧪 Template ID " . $template['id'] . ": " . htmlspecialchars($template['template_name']) . "</h4>\n";
    
    $content = $template['content'];
    $hasIssues = false;
    $issues = [];
    
    // Check for nested img tags
    if (preg_match('/<img\s+src="<img/', $content)) {
        $issues[] = "Nested IMG tags found";
        $hasIssues = true;
    }
    
    // Check for wrong placeholders
    if (strpos($content, '{birthday_member_image_url}') !== false) {
        $issues[] = "Wrong placeholder: {birthday_member_image_url}";
        $hasIssues = true;
    }
    
    // Check for malformed patterns
    if (preg_match('/<[A-Za-z\s]+"[^>]*alt=/', $content)) {
        $issues[] = "Malformed image patterns (missing img tag)";
        $hasIssues = true;
    }
    
    // Check for Sandra Stern corruption
    if (strpos($content, 'Sandra Stern') !== false && strpos($content, '<Sandra Stern') !== false) {
        $issues[] = "Sandra Stern corruption found";
        $hasIssues = true;
    }
    
    // Check for unclosed tags
    if (preg_match('/<\{[^}]+\}/', $content)) {
        $issues[] = "Unclosed tags with placeholders";
        $hasIssues = true;
    }
    
    if ($hasIssues) {
        echo "<div style='background: #f8d7da; padding: 10px; border: 1px solid #f5c6cb;'>\n";
        echo "<h5>❌ Issues Found:</h5>\n";
        foreach ($issues as $issue) {
            echo "<p>• $issue</p>\n";
        }
        echo "</div>\n";
        
        $templatesNeedingFix[] = $template;
    } else {
        echo "<p style='color: green;'>✅ No issues found in this template</p>\n";
    }
    
    echo "</div>\n";
}

if (empty($templatesNeedingFix)) {
    echo "<div style='background: #d4edda; padding: 20px; border: 1px solid #c3e6cb;'>\n";
    echo "<h3>✅ All Templates Are Already Fixed!</h3>\n";
    echo "<p>No birthday templates need fixing.</p>\n";
    echo "</div>\n";
    exit;
}

echo "<h3>🔧 APPLYING FIXES TO " . count($templatesNeedingFix) . " TEMPLATES</h3>\n";

$fixedCount = 0;
$failedCount = 0;

foreach ($templatesNeedingFix as $template) {
    echo "<div style='border: 3px solid #ffc107; margin: 20px 0; padding: 15px; background: #fff3cd;'>\n";
    echo "<h4>🔧 Fixing Template ID " . $template['id'] . ": " . htmlspecialchars($template['template_name']) . "</h4>\n";
    
    $originalContent = $template['content'];
    $fixedContent = $originalContent;
    
    // Apply all the same fixes as before
    $changesApplied = [];
    
    // Fix 1: Remove nested img tags
    $newContent = preg_replace('/<img\s+src="<img\s+src="[^"]*"[^>]*>/', '{member_image}', $fixedContent);
    if ($newContent !== $fixedContent) {
        $changesApplied[] = "Removed nested IMG tags";
        $fixedContent = $newContent;
    }
    
    // Fix 2: Remove any remaining nested patterns
    $newContent = preg_replace('/<img\s+src="<img[^>]*>/', '{member_image}', $fixedContent);
    if ($newContent !== $fixedContent) {
        $changesApplied[] = "Cleaned remaining nested patterns";
        $fixedContent = $newContent;
    }
    
    // Fix 3: Fix wrong placeholders
    $newContent = str_replace('{birthday_member_image_url}', '{birthday_member_photo_url}', $fixedContent);
    if ($newContent !== $fixedContent) {
        $changesApplied[] = "Fixed wrong placeholder: {birthday_member_image_url} → {birthday_member_photo_url}";
        $fixedContent = $newContent;
    }
    
    // Fix 4: Clean up malformed img tags
    $newContent = preg_replace('/<img\s+src="[^"]*<img[^>]*>/', '{member_image}', $fixedContent);
    if ($newContent !== $fixedContent) {
        $changesApplied[] = "Cleaned malformed IMG tags";
        $fixedContent = $newContent;
    }
    
    // Fix 5: Remove stray entities
    $newContent = str_replace('&lt;', '', $fixedContent);
    if ($newContent !== $fixedContent) {
        $changesApplied[] = "Removed stray &lt; entities";
        $fixedContent = $newContent;
    }
    
    // Fix 6: Fix unclosed tags
    $newContent = preg_replace('/<\{([^}]+)\}/', '{$1}', $fixedContent);
    if ($newContent !== $fixedContent) {
        $changesApplied[] = "Fixed unclosed tags with placeholders";
        $fixedContent = $newContent;
    }
    
    // Fix 7: Fix Sandra Stern corruption
    $newContent = preg_replace('/<Sandra Stern"/', '<img src="{birthday_member_photo_url}" alt="Sandra Stern"', $fixedContent);
    if ($newContent !== $fixedContent) {
        $changesApplied[] = "Fixed Sandra Stern corruption";
        $fixedContent = $newContent;
    }
    
    // Fix 8: Fix any remaining broken patterns
    $newContent = preg_replace('/([A-Za-z\s]+)"(\s*alt="[^"]*")(\s*style="[^"]*"[^>]*>)/', '<img src="{birthday_member_photo_url}"$2$3', $fixedContent);
    if ($newContent !== $fixedContent) {
        $changesApplied[] = "Fixed remaining broken image patterns";
        $fixedContent = $newContent;
    }
    
    if (!empty($changesApplied)) {
        echo "<div style='background: #d4edda; padding: 10px; border: 1px solid #c3e6cb;'>\n";
        echo "<h5>✅ Changes Applied:</h5>\n";
        foreach ($changesApplied as $change) {
            echo "<p>• $change</p>\n";
        }
        echo "</div>\n";
        
        // Update the database
        $updateStmt = $pdo->prepare("UPDATE email_templates SET content = ? WHERE id = ?");
        $result = $updateStmt->execute([$fixedContent, $template['id']]);
        
        if ($result) {
            echo "<p style='color: green; font-weight: bold;'>✅ Template ID " . $template['id'] . " updated successfully!</p>\n";
            $fixedCount++;
        } else {
            echo "<p style='color: red; font-weight: bold;'>❌ Failed to update template ID " . $template['id'] . "!</p>\n";
            $failedCount++;
        }
    } else {
        echo "<p style='color: blue;'>ℹ️ No changes needed for template ID " . $template['id'] . "</p>\n";
    }
    
    echo "</div>\n";
}

echo "<div style='border: 3px solid #28a745; margin: 30px 0; padding: 25px; background: #f8fff9;'>\n";
echo "<h3>🎉 BATCH FIX COMPLETE!</h3>\n";
echo "<p><strong>Results:</strong></p>\n";
echo "<ul>\n";
echo "<li>✅ <strong>$fixedCount templates fixed successfully</strong></li>\n";
if ($failedCount > 0) {
    echo "<li>❌ <strong>$failedCount templates failed to update</strong></li>\n";
}
echo "<li>📋 <strong>Total templates processed: " . count($templatesNeedingFix) . "</strong></li>\n";
echo "</ul>\n";

echo "<h4>🧪 IMMEDIATE TEST REQUIRED:</h4>\n";
echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7;'>\n";
echo "<p style='font-size: 18px; color: #856404;'><strong>🔄 TEST ALL 3 TEMPLATES NOW!</strong></p>\n";
echo "<ol>\n";
echo "<li><strong>Go to each birthday template preview</strong></li>\n";
echo "<li><strong>Refresh each one (F5 or Ctrl+R)</strong></li>\n";
echo "<li><strong>Verify all 3 templates now show member images correctly</strong></li>\n";
echo "<li><strong>No more '&lt;Sandra Stern' or broken image patterns</strong></li>\n";
echo "</ol>\n";
echo "</div>\n";

echo "<h4>📝 What Was Fixed Across All Templates:</h4>\n";
echo "<ul>\n";
echo "<li>✅ Removed nested &lt;img src=\"&lt;img src=\" tags</li>\n";
echo "<li>✅ Fixed wrong placeholder usage</li>\n";
echo "<li>✅ Used correct {member_image} or {birthday_member_photo_url} placeholders</li>\n";
echo "<li>✅ Cleaned up malformed HTML</li>\n";
echo "<li>✅ Fixed Sandra Stern corruption</li>\n";
echo "<li>✅ Ensured consistent image rendering across all templates</li>\n";
echo "</ul>\n";

echo "</div>\n";

echo "<div style='background: #e7f3ff; padding: 15px; border: 1px solid #b3d9ff; margin: 20px 0;'>\n";
echo "<h4>🎯 Expected Result for ALL Templates:</h4>\n";
echo "<p><strong>After this fix, ALL 3 birthday templates should show:</strong></p>\n";
echo "<ul>\n";
echo "<li>✅ Birthday member's actual circular photo (not text)</li>\n";
echo "<li>✅ Proper image styling and borders</li>\n";
echo "<li>✅ Correct alt text with member names</li>\n";
echo "<li>✅ Consistent behavior across all 3 templates</li>\n";
echo "<li>❌ No more nested or broken img tags in any template</li>\n";
echo "</ul>\n";
echo "</div>\n";

?>
