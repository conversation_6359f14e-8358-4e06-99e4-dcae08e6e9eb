<?php
/**
 * Test script to verify image embedding is working
 */

require_once 'config.php';

echo "<h2>Image Embedding Test</h2>\n";

try {
    // Test email details
    $testEmail = '<EMAIL>'; // Use the same email from the logs
    $testName = 'Test User';
    $imagePath = 'uploads/profiles/6877ba17a553b.jpg'; // Use the same image from the logs
    
    echo "<h3>Test Details:</h3>\n";
    echo "<p><strong>Test Email:</strong> $testEmail</p>\n";
    echo "<p><strong>Image Path:</strong> $imagePath</p>\n";
    
    // Check if image exists
    if (!file_exists($imagePath)) {
        echo "<p style='color: red;'>❌ Image file does not exist: $imagePath</p>\n";
        exit;
    }
    
    echo "<p style='color: green;'>✅ Image file exists</p>\n";
    
    // Get image info
    $imageSize = filesize($imagePath);
    $mimeType = mime_content_type($imagePath);
    echo "<p><strong>Image Size:</strong> " . number_format($imageSize) . " bytes</p>\n";
    echo "<p><strong>MIME Type:</strong> $mimeType</p>\n";
    
    // Create test email content with embedded image
    $siteUrl = defined('SITE_URL') ? SITE_URL : 'http://localhost/campaign/church';
    $imageUrl = $siteUrl . '/' . ltrim($imagePath, '/');
    
    $emailContent = '
    <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; padding: 20px;">
        <h2 style="color: #333;">Image Embedding Test</h2>
        <p>This is a test email to verify that image embedding is working correctly.</p>
        
        <div style="text-align: center; margin: 20px 0;">
            <img src="' . $imageUrl . '" alt="Test Image" style="width: 200px; height: 200px; border-radius: 50%; object-fit: cover; border: 4px solid #ddd;">
        </div>
        
        <p>If you can see the image above, then image embedding is working correctly.</p>
        <p><strong>Image URL:</strong> ' . htmlspecialchars($imageUrl) . '</p>
        <p><strong>Test Time:</strong> ' . date('Y-m-d H:i:s') . '</p>
    </div>';
    
    // Create member data for birthday notification
    $memberData = [
        'birthday_member_photo_url' => $imageUrl,
        'birthday_member_name' => 'Jennifer',
        'birthday_member_full_name' => 'Jennifer Godson',
        '_original_image_path' => $imagePath,
        '_is_birthday_notification' => true
    ];
    
    echo "<h3>Sending Test Email...</h3>\n";
    
    // Send the email using the same function as birthday reminders
    $result = sendEmail(
        $testEmail,
        $testName,
        'Image Embedding Test - ' . date('H:i:s'),
        $emailContent,
        true, // HTML format
        $memberData
    );
    
    if ($result) {
        echo "<p style='color: green;'>✅ Test email sent successfully!</p>\n";
        echo "<p>Check your email at <strong>$testEmail</strong> to see if the image displays correctly.</p>\n";
        
        // Check the latest birthday image embedding log
        echo "<h3>Latest Log Entry:</h3>\n";
        $logContent = file_get_contents('logs/birthday_image_embedding.log');
        $logLines = explode("\n", $logContent);
        $latestEntry = [];
        $inEntry = false;
        
        for ($i = count($logLines) - 1; $i >= 0; $i--) {
            if (strpos($logLines[$i], '---') !== false && $inEntry) {
                break;
            }
            if (strpos($logLines[$i], 'SUCCESS: Embedded birthday member image') !== false) {
                $inEntry = true;
            }
            if ($inEntry) {
                array_unshift($latestEntry, $logLines[$i]);
            }
        }
        
        if (!empty($latestEntry)) {
            echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd;'>";
            echo htmlspecialchars(implode("\n", $latestEntry));
            echo "</pre>\n";
        }
        
    } else {
        echo "<p style='color: red;'>❌ Failed to send test email</p>\n";
        global $last_email_error;
        if ($last_email_error) {
            echo "<p style='color: red;'>Error: " . htmlspecialchars($last_email_error) . "</p>\n";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}
?>
