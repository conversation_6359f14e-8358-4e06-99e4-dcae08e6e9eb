<?php
/**
 * Debug script to see exactly what's in the email content
 */

require_once 'config.php';

echo "<h2>Email Content Debug</h2>\n";

try {
    // Get <PERSON> (the birthday member from the logs)
    $stmt = $pdo->prepare("SELECT * FROM members WHERE full_name LIKE '%Jennifer%' LIMIT 1");
    $stmt->execute();
    $birthdayMember = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$birthdayMember) {
        echo "❌ Jennifer not found\n";
        exit;
    }
    
    // Get a recipient
    $stmt = $pdo->prepare("SELECT * FROM members WHERE id != ? AND status = 'active' LIMIT 1");
    $stmt->execute([$birthdayMember['id']]);
    $recipient = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<h3>Debug Info:</h3>\n";
    echo "<p><strong>Birthday Member:</strong> {$birthdayMember['full_name']}</p>\n";
    echo "<p><strong>Birthday Member Image:</strong> {$birthdayMember['image_path']}</p>\n";
    echo "<p><strong>Recipient:</strong> {$recipient['full_name']}</p>\n";
    
    // Create the URLs as they would be in send_birthday_reminders.php
    $siteUrl = defined('SITE_URL') ? SITE_URL : 'http://localhost/campaign/church';
    $birthdayPhotoUrl = $siteUrl . '/' . ltrim($birthdayMember['image_path'], '/');
    
    echo "<p><strong>Expected Birthday Photo URL:</strong> " . htmlspecialchars($birthdayPhotoUrl) . "</p>\n";
    
    // Get a birthday template
    $stmt = $pdo->prepare("SELECT * FROM email_templates WHERE is_birthday_template = 1 LIMIT 1");
    $stmt->execute();
    $template = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$template) {
        echo "❌ No birthday template found\n";
        exit;
    }
    
    echo "<h3>Template: {$template['template_name']}</h3>\n";
    
    // Create member data exactly as it would be in send_birthday_reminders.php
    $memberData = [
        'first_name' => $recipient['first_name'],
        'full_name' => $recipient['full_name'],
        'email' => $recipient['email'],
        'birthday_member_name' => $birthdayMember['first_name'],
        'birthday_member_first_name' => $birthdayMember['first_name'],
        'birthday_member_full_name' => $birthdayMember['full_name'],
        'birthday_member_photo_url' => $birthdayPhotoUrl,
        'birthday_member_image' => $birthdayPhotoUrl,
        'member_image' => $birthdayPhotoUrl,
        'member_image_url' => $birthdayPhotoUrl,
        'image_path' => $birthdayPhotoUrl,
        'profile_photo' => $birthdayPhotoUrl,
        '_original_image_path' => $birthdayMember['image_path'],
        '_is_birthday_notification' => true
    ];
    
    // Process the template
    $content = replaceTemplatePlaceholders($template['content'], $memberData);
    
    echo "<h3>Processed Email Content:</h3>\n";
    echo "<div style='border: 1px solid #ddd; padding: 10px; background: #f9f9f9; max-height: 400px; overflow-y: auto;'>\n";
    echo htmlspecialchars($content);
    echo "</div>\n";
    
    echo "<h3>Image URL Analysis:</h3>\n";
    
    // Extract all image URLs from the content
    preg_match_all('/<img[^>]+src=["\']([^"\']+)["\'][^>]*>/i', $content, $matches);
    
    if (!empty($matches[1])) {
        echo "<h4>Image URLs found in content:</h4>\n";
        foreach ($matches[1] as $i => $imgUrl) {
            echo "<p><strong>Image " . ($i + 1) . ":</strong> " . htmlspecialchars($imgUrl) . "</p>\n";
            
            // Check exact match
            if ($imgUrl === $birthdayPhotoUrl) {
                echo "<span style='color: green;'>✅ EXACT MATCH with birthday photo URL</span><br>\n";
            } else {
                echo "<span style='color: red;'>❌ NO MATCH with birthday photo URL</span><br>\n";
                echo "<span style='color: blue;'>Expected: " . htmlspecialchars($birthdayPhotoUrl) . "</span><br>\n";
                echo "<span style='color: orange;'>Found: " . htmlspecialchars($imgUrl) . "</span><br>\n";
                
                // Check for differences
                if (strlen($imgUrl) !== strlen($birthdayPhotoUrl)) {
                    echo "<span style='color: purple;'>Length difference: Found=" . strlen($imgUrl) . ", Expected=" . strlen($birthdayPhotoUrl) . "</span><br>\n";
                }
                
                // Check for character differences
                $diff = array_diff_assoc(str_split($imgUrl), str_split($birthdayPhotoUrl));
                if (!empty($diff)) {
                    echo "<span style='color: purple;'>Character differences at positions: " . implode(', ', array_keys($diff)) . "</span><br>\n";
                }
            }
            echo "<br>\n";
        }
    } else {
        echo "<p>❌ No image URLs found in content</p>\n";
    }
    
    // Test URL replacement
    echo "<h3>URL Replacement Test:</h3>\n";
    $testContent = $content;
    $originalContent = $content;
    
    // Try the exact replacement that config.php would do
    $testContent = str_replace($birthdayPhotoUrl, 'cid:test_replacement', $testContent);
    
    if ($testContent !== $originalContent) {
        echo "<p style='color: green;'>✅ URL replacement WOULD work</p>\n";
    } else {
        echo "<p style='color: red;'>❌ URL replacement WOULD fail</p>\n";
        
        // Try to find what's actually in the content that might be close
        echo "<h4>Debugging URL replacement failure:</h4>\n";
        
        // Check if any part of the URL exists
        $urlParts = parse_url($birthdayPhotoUrl);
        $filename = basename($birthdayPhotoUrl);
        
        if (strpos($content, $filename) !== false) {
            echo "<p style='color: orange;'>⚠️ Filename '$filename' found in content, but full URL doesn't match</p>\n";
        }
        
        if (strpos($content, $urlParts['host']) !== false) {
            echo "<p style='color: orange;'>⚠️ Host '{$urlParts['host']}' found in content</p>\n";
        }
        
        // Show raw content for manual inspection
        echo "<h4>Raw Content (first 1000 chars):</h4>\n";
        echo "<pre style='background: #f0f0f0; padding: 10px; font-size: 12px; overflow-x: auto;'>";
        echo htmlspecialchars(substr($content, 0, 1000));
        echo "</pre>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}
?>
