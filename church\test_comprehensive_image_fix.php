<?php
require_once 'config.php';
require_once 'send_birthday_reminders.php';

echo "<h2>🧪 COMPREHENSIVE TEST: Image Placeholder Fix</h2>\n";

global $pdo;

try {
    // Get a test member with an image
    $stmt = $pdo->prepare("SELECT * FROM members WHERE image_path IS NOT NULL AND image_path != '' LIMIT 1");
    $stmt->execute();
    $testMember = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$testMember) {
        echo "<p style='color: red;'>❌ No test member with image found!</p>\n";
        exit;
    }
    
    echo "<h3>📋 Test Setup</h3>\n";
    echo "<p><strong>Test Member:</strong> " . htmlspecialchars($testMember['full_name']) . "</p>\n";
    echo "<p><strong>Image Path:</strong> " . htmlspecialchars($testMember['image_path']) . "</p>\n";
    
    // Get notification templates
    $stmt = $pdo->prepare("SELECT * FROM email_templates WHERE template_name LIKE '%Member Upcoming Birthday Notification%' ORDER BY template_name");
    $stmt->execute();
    $templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($templates)) {
        echo "<p style='color: red;'>❌ No notification templates found!</p>\n";
        exit;
    }
    
    echo "<p><strong>Templates to test:</strong> " . count($templates) . "</p>\n";
    
    // Create birthday reminder instance
    $birthdayReminder = new BirthdayReminder($pdo);
    
    // Create a sample recipient (different from birthday member)
    $recipient = [
        'id' => 999,
        'full_name' => 'John Doe',
        'first_name' => 'John',
        'email' => '<EMAIL>'
    ];
    
    echo "<p><strong>Recipient:</strong> " . htmlspecialchars($recipient['full_name']) . "</p>\n";
    echo "<p><strong>Birthday Member:</strong> " . htmlspecialchars($testMember['full_name']) . "</p>\n";
    
    foreach ($templates as $template) {
        echo "<div style='border: 2px solid #007bff; margin: 20px 0; padding: 15px;'>\n";
        echo "<h3>🧪 Testing: " . htmlspecialchars($template['template_name']) . "</h3>\n";
        
        // Check original template for corruption
        if (strpos($template['content'], 'Sandra Stern') !== false) {
            echo "<p style='color: red;'>⚠️ Original template still contains 'Sandra Stern'</p>\n";
        } else {
            echo "<p style='color: green;'>✅ Original template does not contain 'Sandra Stern'</p>\n";
        }
        
        // Test subject processing
        echo "<h4>📝 Subject Processing</h4>\n";
        $originalSubject = $template['subject'];
        $processedSubject = $birthdayReminder->processBirthdayMemberTemplate($originalSubject, $recipient, $testMember, 3);
        
        echo "<p><strong>Original:</strong> " . htmlspecialchars($originalSubject) . "</p>\n";
        echo "<p><strong>Processed:</strong> " . htmlspecialchars($processedSubject) . "</p>\n";
        
        if (strpos($processedSubject, 'Sandra Stern') !== false) {
            echo "<p style='color: red;'>❌ Subject still contains 'Sandra Stern'!</p>\n";
        } else {
            echo "<p style='color: green;'>✅ Subject clean</p>\n";
        }
        
        // Test content processing
        echo "<h4>📄 Content Processing</h4>\n";
        $originalContent = $template['content'];
        $processedContent = $birthdayReminder->processBirthdayMemberTemplate($originalContent, $recipient, $testMember, 3);
        
        // Check for corruption in processed content
        if (strpos($processedContent, 'Sandra Stern') !== false) {
            echo "<p style='color: red;'>❌ Processed content still contains 'Sandra Stern'!</p>\n";
            
            // Show where it appears
            $pos = strpos($processedContent, 'Sandra Stern');
            $start = max(0, $pos - 100);
            $length = 250;
            $context = substr($processedContent, $start, $length);
            echo "<div style='background: #f8d7da; padding: 10px; border: 1px solid #f5c6cb;'>\n";
            echo "<strong>Context:</strong><br>\n";
            echo "<code>" . htmlspecialchars($context) . "</code>\n";
            echo "</div>\n";
        } else {
            echo "<p style='color: green;'>✅ Processed content does not contain 'Sandra Stern'</p>\n";
        }
        
        // Check for proper image tags
        if (preg_match_all('/<img[^>]*>/i', $processedContent, $matches)) {
            echo "<p style='color: green;'>✅ Found " . count($matches[0]) . " image tags</p>\n";
            
            foreach ($matches[0] as $i => $imgTag) {
                echo "<h5>Image " . ($i + 1) . ":</h5>\n";
                echo "<div style='background: #e7f3ff; padding: 10px; margin: 5px 0; border-left: 4px solid #007bff;'>\n";
                echo "<code>" . htmlspecialchars($imgTag) . "</code>\n";
                
                // Check src attribute
                if (preg_match('/src="([^"]*)"/', $imgTag, $srcMatch)) {
                    $src = $srcMatch[1];
                    echo "<br><strong>Source:</strong> " . htmlspecialchars($src) . "\n";
                    
                    if (strpos($src, 'http') === 0) {
                        echo "<br><span style='color: green;'>✅ Valid URL format</span>\n";
                    } else {
                        echo "<br><span style='color: red;'>❌ Invalid URL format</span>\n";
                    }
                    
                    if (strpos($src, $testMember['image_path']) !== false) {
                        echo "<br><span style='color: green;'>✅ Uses birthday member's image</span>\n";
                    } else {
                        echo "<br><span style='color: orange;'>⚠️ Different image (might be default)</span>\n";
                    }
                }
                
                // Check alt attribute
                if (preg_match('/alt="([^"]*)"/', $imgTag, $altMatch)) {
                    $alt = $altMatch[1];
                    echo "<br><strong>Alt text:</strong> " . htmlspecialchars($alt) . "\n";
                    
                    if (strpos($alt, $testMember['full_name']) !== false) {
                        echo "<br><span style='color: green;'>✅ Alt text contains birthday member's name</span>\n";
                    } elseif (strpos($alt, explode(' ', $testMember['full_name'])[0]) !== false) {
                        echo "<br><span style='color: green;'>✅ Alt text contains birthday member's first name</span>\n";
                    } else {
                        echo "<br><span style='color: orange;'>⚠️ Alt text: " . htmlspecialchars($alt) . "</span>\n";
                    }
                } else {
                    echo "<br><span style='color: red;'>❌ No alt attribute found</span>\n";
                }
                
                echo "</div>\n";
            }
        } else {
            echo "<p style='color: red;'>❌ No image tags found in processed content!</p>\n";
        }
        
        // Check for broken patterns
        $brokenPatterns = [
            '/[A-Za-z\s]+"[^>]*alt="[^"]*"[^>]*style="[^"]*"[^>]*>/',
            '/' . preg_quote($testMember['full_name'], '/') . '"[^>]*alt=/',
            '/Sandra Stern"[^>]*alt=/',
            '/Member"[^>]*alt=/'
        ];
        
        $foundBrokenPatterns = false;
        foreach ($brokenPatterns as $i => $pattern) {
            if (preg_match($pattern, $processedContent)) {
                echo "<p style='color: red;'>❌ Found broken pattern " . ($i + 1) . " in processed content!</p>\n";
                $foundBrokenPatterns = true;
                
                // Show the broken pattern
                if (preg_match($pattern, $processedContent, $match)) {
                    echo "<p><strong>Broken pattern:</strong> <code>" . htmlspecialchars($match[0]) . "</code></p>\n";
                }
            }
        }
        
        if (!$foundBrokenPatterns) {
            echo "<p style='color: green;'>✅ No broken image patterns found</p>\n";
        }
        
        // Check for unreplaced placeholders
        if (preg_match_all('/\{[^}]+\}/', $processedContent, $placeholderMatches)) {
            $unreplacedPlaceholders = array_unique($placeholderMatches[0]);
            if (!empty($unreplacedPlaceholders)) {
                echo "<p style='color: orange;'>⚠️ Unreplaced placeholders: " . implode(', ', $unreplacedPlaceholders) . "</p>\n";
            }
        } else {
            echo "<p style='color: green;'>✅ All placeholders replaced</p>\n";
        }
        
        echo "</div>\n";
    }
    
    // Test actual email sending
    echo "<div style='border: 3px solid #28a745; margin: 20px 0; padding: 20px; background: #f8fff9;'>\n";
    echo "<h3>📧 Email Sending Test</h3>\n";
    echo "<p style='color: orange;'>⚠️ <strong>Note:</strong> This will send a real test email!</p>\n";
    
    $firstTemplate = $templates[0];
    echo "<p><strong>Using template:</strong> " . htmlspecialchars($firstTemplate['template_name']) . "</p>\n";
    
    echo "<button onclick='sendTestEmail()' style='background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>Send Test Email</button>\n";
    
    echo "<div id='testResult' style='margin-top: 15px;'></div>\n";
    
    echo "<script>
    function sendTestEmail() {
        document.getElementById('testResult').innerHTML = '<p style=\"color: blue;\">🔄 Sending test email...</p>';
        
        fetch('test_send_birthday_notification.php?member_id={$testMember['id']}&template_id={$firstTemplate['id']}')
            .then(response => response.text())
            .then(result => {
                document.getElementById('testResult').innerHTML = result;
            })
            .catch(error => {
                document.getElementById('testResult').innerHTML = '<p style=\"color: red;\">❌ Error: ' + error + '</p>';
            });
    }
    </script>\n";
    
    echo "</div>\n";
    
    echo "<div style='border: 2px solid #17a2b8; margin: 20px 0; padding: 15px; background: #f1f9fc;'>\n";
    echo "<h3>🎯 Test Summary</h3>\n";
    echo "<p>This comprehensive test verifies:</p>\n";
    echo "<ul>\n";
    echo "<li>✅ Template corruption is eliminated</li>\n";
    echo "<li>✅ Proper image HTML tags are generated</li>\n";
    echo "<li>✅ Alt text shows correct birthday member names</li>\n";
    echo "<li>✅ No broken image patterns remain</li>\n";
    echo "<li>✅ All placeholders are properly replaced</li>\n";
    echo "<li>✅ Email sending works with embedded images</li>\n";
    echo "</ul>\n";
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red; font-weight: bold;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "<p>Stack trace:</p>\n";
    echo "<pre style='background: #f8f9fa; padding: 10px; font-size: 12px;'>" . htmlspecialchars($e->getTraceAsString()) . "</pre>\n";
}

?>
