<?php
require_once 'config.php';
require_once 'send_birthday_reminders.php';

header('Content-Type: text/html; charset=utf-8');

$memberId = $_GET['member_id'] ?? null;
$templateId = $_GET['template_id'] ?? null;

if (!$memberId || !$templateId) {
    echo "<p style='color: red;'>❌ Missing member_id or template_id parameters</p>";
    exit;
}

echo "<h4>🧪 Birthday Notification Test</h4>\n";

global $pdo;

try {
    // Get the test member
    $stmt = $pdo->prepare("SELECT * FROM members WHERE id = ?");
    $stmt->execute([$memberId]);
    $testMember = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$testMember) {
        echo "<p style='color: red;'>❌ Test member not found</p>";
        exit;
    }
    
    // Get the template
    $stmt = $pdo->prepare("SELECT * FROM email_templates WHERE id = ?");
    $stmt->execute([$templateId]);
    $template = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$template) {
        echo "<p style='color: red;'>❌ Template not found</p>";
        exit;
    }
    
    echo "<p><strong>Test Member:</strong> " . htmlspecialchars($testMember['full_name']) . "</p>\n";
    echo "<p><strong>Template:</strong> " . htmlspecialchars($template['template_name']) . "</p>\n";
    echo "<p><strong>Member Image:</strong> " . htmlspecialchars($testMember['image_path'] ?? 'None') . "</p>\n";
    
    // Create birthday reminder instance
    $birthdayReminder = new BirthdayReminder($pdo);
    
    // Send the notification
    $result = $birthdayReminder->sendMemberBirthdayNotifications($testMember['id'], $template['id'], 0);
    
    if ($result && (is_array($result) ? count($result) > 0 : $result > 0)) {
        $count = is_array($result) ? count($result) : $result;
        echo "<p style='color: green; font-weight: bold;'>✅ Birthday notification sent successfully!</p>\n";
        echo "<p><strong>Recipients:</strong> $count</p>\n";
        echo "<p>📧 Check your email to verify:</p>\n";
        echo "<ul>\n";
        echo "<li>✅ Image appears correctly (not empty circle)</li>\n";
        echo "<li>✅ Alt text shows: <strong>" . htmlspecialchars($testMember['full_name']) . "</strong></li>\n";
        echo "<li>✅ Email content is about " . htmlspecialchars($testMember['full_name']) . "'s birthday</li>\n";
        echo "</ul>\n";
        
        // Check the logs for verification
        echo "<h5>📋 Log Verification:</h5>\n";
        
        // Check email debug log
        $logFile = 'logs/email_debug.log';
        if (file_exists($logFile)) {
            $logContent = file_get_contents($logFile);
            $lines = explode("\n", $logContent);
            $recentLines = array_slice($lines, -20); // Last 20 lines
            
            $foundImageEmbedding = false;
            $foundCorrectAlt = false;
            
            foreach ($recentLines as $line) {
                if (strpos($line, 'birthday_member_image_') !== false) {
                    $foundImageEmbedding = true;
                }
                if (strpos($line, 'alt="' . $testMember['full_name'] . '"') !== false) {
                    $foundCorrectAlt = true;
                }
            }
            
            if ($foundImageEmbedding) {
                echo "<p style='color: green;'>✅ Image embedding detected in logs</p>\n";
            } else {
                echo "<p style='color: orange;'>⚠️ Image embedding not clearly detected in recent logs</p>\n";
            }
            
            if ($foundCorrectAlt) {
                echo "<p style='color: green;'>✅ Correct alt text detected in logs</p>\n";
            } else {
                echo "<p style='color: orange;'>⚠️ Correct alt text not clearly detected in recent logs</p>\n";
            }
        }
        
        // Check birthday image embedding log
        $birthdayLogFile = 'logs/birthday_image_embedding.log';
        if (file_exists($birthdayLogFile)) {
            $logContent = file_get_contents($birthdayLogFile);
            $lines = explode("\n", $logContent);
            $recentLines = array_slice($lines, -10); // Last 10 lines
            
            $foundUrlReplacement = false;
            foreach ($recentLines as $line) {
                if (strpos($line, 'URL replaced in email body: YES') !== false) {
                    $foundUrlReplacement = true;
                    break;
                }
            }
            
            if ($foundUrlReplacement) {
                echo "<p style='color: green;'>✅ URL replacement successful in email body</p>\n";
            } else {
                echo "<p style='color: orange;'>⚠️ URL replacement status unclear in recent logs</p>\n";
            }
        }
        
    } else {
        echo "<p style='color: red; font-weight: bold;'>❌ Birthday notification failed!</p>\n";
        echo "<p>Check the logs for error details.</p>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red; font-weight: bold;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "<p>Stack trace:</p>\n";
    echo "<pre style='background: #f8f9fa; padding: 10px; font-size: 12px;'>" . htmlspecialchars($e->getTraceAsString()) . "</pre>\n";
}

?>
