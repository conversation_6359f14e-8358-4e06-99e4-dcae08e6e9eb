<?php
require_once 'config.php';

echo "<h2>🧪 COMPREHENSIVE TEST: Template Fix Verification</h2>\n";

global $pdo;

// Test 1: Check if templates are fixed
echo "<div style='border: 2px solid #007bff; margin: 20px 0; padding: 15px;'>\n";
echo "<h3>📋 Test 1: Template Content Verification</h3>\n";

$templateNames = [
    'Member Upcoming Birthday Notification 1',
    'Member Upcoming Birthday Notification 2', 
    'Member Upcoming Birthday Notification 3'
];

$allFixed = true;
foreach ($templateNames as $templateName) {
    $stmt = $pdo->prepare("SELECT id, template_name, content FROM email_templates WHERE template_name = ?");
    $stmt->execute([$templateName]);
    $template = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($template) {
        echo "<h4>✅ " . htmlspecialchars($template['template_name']) . "</h4>\n";
        
        // Check for corruption patterns
        $hasCorruption = preg_match('/[A-Za-z\s]+"[^>]*alt="[^"]*"[^>]*style="[^"]*"[^>]*>/', $template['content']);
        
        if ($hasCorruption) {
            echo "<p style='color: red;'>❌ Still has corruption!</p>\n";
            $allFixed = false;
        } else {
            echo "<p style='color: green;'>✅ No corruption found</p>\n";
        }
        
        // Check for proper image placeholders
        $hasImagePlaceholder = false;
        $placeholders = ['{birthday_member_image}', '{member_image}', '{birthday_member_photo_url}'];
        foreach ($placeholders as $placeholder) {
            if (strpos($template['content'], $placeholder) !== false) {
                echo "<p style='color: green;'>✅ Has image placeholder: <code>$placeholder</code></p>\n";
                $hasImagePlaceholder = true;
                break;
            }
        }
        
        if (!$hasImagePlaceholder) {
            echo "<p style='color: red;'>❌ No image placeholders found!</p>\n";
            $allFixed = false;
        }
        
        // Check for proper img tags
        if (preg_match_all('/<img[^>]*>/i', $template['content'], $matches)) {
            echo "<p style='color: blue;'>ℹ️ Found " . count($matches[0]) . " img tags</p>\n";
            foreach ($matches[0] as $i => $imgTag) {
                if (strpos($imgTag, 'src="{') !== false) {
                    echo "<p style='color: green; margin-left: 20px;'>✅ Image " . ($i + 1) . " uses placeholder</p>\n";
                } else {
                    echo "<p style='color: red; margin-left: 20px;'>❌ Image " . ($i + 1) . " has hardcoded src</p>\n";
                    $allFixed = false;
                }
            }
        }
    } else {
        echo "<h4>❌ Template not found: " . htmlspecialchars($templateName) . "</h4>\n";
        $allFixed = false;
    }
    echo "<hr>\n";
}

if ($allFixed) {
    echo "<p style='color: green; font-weight: bold; font-size: 16px;'>🎉 ALL TEMPLATES ARE FIXED!</p>\n";
} else {
    echo "<p style='color: red; font-weight: bold; font-size: 16px;'>❌ Some templates still need fixing!</p>\n";
}
echo "</div>\n";

// Test 2: Test placeholder replacement
echo "<div style='border: 2px solid #28a745; margin: 20px 0; padding: 15px;'>\n";
echo "<h3>🔄 Test 2: Placeholder Replacement</h3>\n";

// Get a test member with image
$stmt = $pdo->prepare("SELECT * FROM members WHERE image_path IS NOT NULL AND image_path != '' LIMIT 1");
$stmt->execute();
$testMember = $stmt->fetch(PDO::FETCH_ASSOC);

if ($testMember) {
    echo "<p><strong>Test Member:</strong> " . htmlspecialchars($testMember['full_name']) . "</p>\n";
    echo "<p><strong>Image Path:</strong> " . htmlspecialchars($testMember['image_path']) . "</p>\n";
    
    // Test placeholder replacement
    $testContent = '<img src="{birthday_member_image}" alt="{birthday_member_full_name}" style="width: 150px; height: 150px;">';
    
    $memberData = [
        'birthday_member_full_name' => $testMember['full_name'],
        'birthday_member_image' => 'http://localhost/campaign/church/' . ltrim($testMember['image_path'], '/'),
        'image_path' => $testMember['image_path']
    ];
    
    $processedContent = replaceTemplatePlaceholders($testContent, $memberData);
    
    echo "<h4>📝 Placeholder Replacement Test:</h4>\n";
    echo "<p><strong>Before:</strong></p>\n";
    echo "<code style='background: #f8f9fa; padding: 10px; display: block;'>" . htmlspecialchars($testContent) . "</code>\n";
    echo "<p><strong>After:</strong></p>\n";
    echo "<code style='background: #d4edda; padding: 10px; display: block;'>" . htmlspecialchars($processedContent) . "</code>\n";
    
    // Check if replacement worked
    if (strpos($processedContent, '{birthday_member_image}') === false && 
        strpos($processedContent, '{birthday_member_full_name}') === false) {
        echo "<p style='color: green; font-weight: bold;'>✅ Placeholder replacement working correctly!</p>\n";
    } else {
        echo "<p style='color: red; font-weight: bold;'>❌ Placeholder replacement failed!</p>\n";
    }
    
    // Test the actual rendered result
    echo "<h4>🖼️ Rendered Result:</h4>\n";
    echo "<div style='border: 1px solid #ddd; padding: 15px; background: white;'>\n";
    echo $processedContent;
    echo "</div>\n";
    
} else {
    echo "<p style='color: red;'>❌ No test member with image found!</p>\n";
}
echo "</div>\n";

// Test 3: Test birthday notification sending
echo "<div style='border: 2px solid #ffc107; margin: 20px 0; padding: 15px;'>\n";
echo "<h3>📧 Test 3: Birthday Notification System</h3>\n";

if ($testMember) {
    // Get a birthday template
    $stmt = $pdo->prepare("SELECT * FROM email_templates WHERE template_name LIKE '%Member Upcoming Birthday Notification%' LIMIT 1");
    $stmt->execute();
    $template = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($template) {
        echo "<p><strong>Test Template:</strong> " . htmlspecialchars($template['template_name']) . "</p>\n";
        
        try {
            require_once 'send_birthday_reminders.php';
            $birthdayReminder = new BirthdayReminder($pdo);
            
            // Test sending (this will actually send an email)
            echo "<p style='color: orange;'>⚠️ <strong>Note:</strong> This will send a real test email!</p>\n";
            echo "<button onclick='sendTestEmail()' style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>Send Test Email</button>\n";
            
            echo "<div id='testResult' style='margin-top: 15px;'></div>\n";
            
            echo "<script>
            function sendTestEmail() {
                document.getElementById('testResult').innerHTML = '<p style=\"color: blue;\">🔄 Sending test email...</p>';
                
                fetch('test_send_birthday_notification.php?member_id={$testMember['id']}&template_id={$template['id']}')
                    .then(response => response.text())
                    .then(result => {
                        document.getElementById('testResult').innerHTML = result;
                    })
                    .catch(error => {
                        document.getElementById('testResult').innerHTML = '<p style=\"color: red;\">❌ Error: ' + error + '</p>';
                    });
            }
            </script>\n";
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Error loading birthday reminder system: " . htmlspecialchars($e->getMessage()) . "</p>\n";
        }
    } else {
        echo "<p style='color: red;'>❌ No birthday template found for testing!</p>\n";
    }
} else {
    echo "<p style='color: red;'>❌ No test member available!</p>\n";
}
echo "</div>\n";

echo "<div style='border: 3px solid #28a745; margin: 20px 0; padding: 20px; background: #f8fff9;'>\n";
echo "<h3>🎯 SUMMARY</h3>\n";
echo "<p>This comprehensive test verifies:</p>\n";
echo "<ol>\n";
echo "<li>✅ Template corruption has been fixed</li>\n";
echo "<li>✅ Image placeholders are properly inserted</li>\n";
echo "<li>✅ Placeholder replacement system works</li>\n";
echo "<li>✅ Birthday notification system is functional</li>\n";
echo "</ol>\n";
echo "<p><strong>Expected Results:</strong></p>\n";
echo "<ul>\n";
echo "<li>Template previews should show member images correctly</li>\n";
echo "<li>Email notifications should embed images properly</li>\n";
echo "<li>Alt text should show correct member names</li>\n";
echo "<li>No more 'Sandra Stern' corruption in templates</li>\n";
echo "</ul>\n";
echo "</div>\n";

?>
