<?php
require_once 'config.php';

$stmt = $pdo->prepare('SELECT template_name, content FROM email_templates WHERE template_name LIKE "%notification%" LIMIT 1');
$stmt->execute();
$template = $stmt->fetch();

if ($template) {
    echo "Template: {$template['template_name']}\n\n";
    
    // Look for the photo div
    if (preg_match('/<div[^>]*class=["\']photo["\'][^>]*>.*?<\/div>/s', $template['content'], $matches)) {
        echo "Photo div content:\n";
        echo $matches[0] . "\n\n";
    }
    
    // Look for any img tags
    if (preg_match_all('/<img[^>]*>/i', $template['content'], $matches)) {
        echo "IMG tags found:\n";
        foreach ($matches[0] as $img) {
            echo $img . "\n";
        }
    } else {
        echo "No IMG tags found in template\n";
    }
    
    // Look for birthday_member_photo_url usage
    if (strpos($template['content'], 'birthday_member_photo_url') !== false) {
        echo "\nTemplate contains birthday_member_photo_url placeholder\n";
        
        // Show context around it
        $pos = strpos($template['content'], 'birthday_member_photo_url');
        $start = max(0, $pos - 100);
        $length = 200;
        $context = substr($template['content'], $start, $length);
        echo "Context:\n" . $context . "\n";
    }
}
?>
